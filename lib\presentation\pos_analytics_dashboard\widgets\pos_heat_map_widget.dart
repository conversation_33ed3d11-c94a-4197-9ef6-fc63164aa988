import 'package:flutter/material.dart';

class PosHeatMapWidget extends StatelessWidget {
  const PosHeatMapWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Peak Hours Heat Map',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            _buildHeatMap(),
            SizedBox(height: 16),
            _buildLegend(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeatMap() {
    final hours = ['6AM', '8AM', '10AM', '12PM', '2PM', '4PM', '6PM', '8PM', '10PM'];
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Column(
      children: [
        Row(
          children: [
            Container(width: 40),
            ...hours.map((hour) => Expanded(
                  child: Center(
                    child: Text(
                      hour,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                )),
          ],
        ),
        SizedBox(height: 8),
        ...days.map((day) => _buildHeatMapRow(day, hours.length)),
      ],
    );
  }

  Widget _buildHeatMapRow(String day, int hoursCount) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ...List.generate(hoursCount, (index) {
            double intensity = _getHeatIntensity(day, index);
            return Expanded(
              child: Container(
                height: 20,
                margin: EdgeInsets.symmetric(horizontal: 1),
                decoration: BoxDecoration(
                  color: _getHeatColor(intensity),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  double _getHeatIntensity(String day, int hour) {
    // Mock data for demonstration
    if (day == 'Sat' || day == 'Sun') {
      if (hour >= 4 && hour <= 6) return 0.9; // Peak weekend dinner
      if (hour >= 2 && hour <= 4) return 0.7; // Weekend lunch
      return 0.3;
    } else {
      if (hour >= 4 && hour <= 6) return 0.8; // Weekday dinner
      if (hour >= 2 && hour <= 3) return 0.6; // Weekday lunch
      if (hour >= 0 && hour <= 1) return 0.4; // Breakfast
      return 0.2;
    }
  }

  Color _getHeatColor(double intensity) {
    if (intensity >= 0.8) return Colors.red;
    if (intensity >= 0.6) return Colors.orange;
    if (intensity >= 0.4) return Colors.yellow;
    if (intensity >= 0.2) return Colors.green.shade300;
    return Colors.grey.shade300;
  }

  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Low',
          style: TextStyle(fontSize: 10),
        ),
        SizedBox(width: 8),
        ...List.generate(
            5,
            (index) => Container(
                  width: 12,
                  height: 12,
                  margin: EdgeInsets.symmetric(horizontal: 1),
                  decoration: BoxDecoration(
                    color: _getHeatColor(index * 0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                )),
        SizedBox(width: 8),
        Text(
          'High',
          style: TextStyle(fontSize: 10),
        ),
      ],
    );
  }
}
