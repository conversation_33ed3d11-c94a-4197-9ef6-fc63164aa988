import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/app_export.dart';
import './widgets/delivery_address_widget.dart';
import './widgets/final_order_review_widget.dart';
import './widgets/order_timing_widget.dart';
import './widgets/payment_method_widget.dart';
import './widgets/tip_selection_widget.dart';

class Checkout extends StatefulWidget {
  const Checkout({super.key});

  @override
  State<Checkout> createState() => _CheckoutState();
}

class _CheckoutState extends State<Checkout> with TickerProviderStateMixin {
  late AnimationController _loadingAnimationController;
  late AnimationController _successAnimationController;

  // Order data (typically passed from previous screen)
  List<Map<String, dynamic>> cartItems = [];
  double subtotal = 0.0;
  double deliveryFee = 2.99;
  double serviceFee = 1.50;
  double taxes = 0.0;
  double discount = 0.0;
  double tip = 0.0;
  double total = 0.0;
  String? appliedPromoCode;
  String deliveryInstructions = '';
  Map<String, dynamic> restaurantData = {};

  // Checkout state
  bool isProcessingPayment = false;

  // Mock current address
  Map<String, dynamic> currentAddress = {
    "id": "addr_1",
    "type": "Home",
    "fullAddress": "123 Main Street, Apartment 4B, New York, NY 10001",
    "unit": "4B",
    "isDefault": true,
    "estimatedDelivery": "25-35 min"
  };

  // Mock payment methods
  List<Map<String, dynamic>> paymentMethods = [
    {
      "id": "card_1",
      "type": "credit_card",
      "name": "Visa ending in 4242",
      "details": "Expires 12/25",
      "isDefault": true
    },
    {"id": "apple_pay", "type": "apple_pay", "name": "Apple Pay", "details": "Touch ID or Face ID", "isDefault": false},
    {
      "id": "google_pay",
      "type": "google_pay",
      "name": "Google Pay",
      "details": "Fingerprint or PIN",
      "isDefault": false
    }
  ];

  String? selectedPaymentId;
  String orderTiming = 'asap';
  DateTime? scheduledTime;

  @override
  void initState() {
    super.initState();
    _loadingAnimationController = AnimationController(duration: const Duration(seconds: 2), vsync: this);
    _successAnimationController = AnimationController(duration: const Duration(milliseconds: 800), vsync: this);

    // Set default payment method
    selectedPaymentId = paymentMethods.firstWhere((method) => method['isDefault'] == true)['id'];

    // Initialize with data from cart (in real app, this would come from route arguments)
    _initializeOrderData();
  }

  @override
  void dispose() {
    _loadingAnimationController.dispose();
    _successAnimationController.dispose();
    super.dispose();
  }

  void _initializeOrderData() {
    // Mock data - in real app this would come from route arguments
    cartItems = [
      {
        "id": 1,
        "name": "Spaghetti Carbonara",
        "price": "\$18.99",
        "quantity": 2,
        "customization": {
          "size": "Regular",
          "spiceLevel": "Medium",
          "specialInstructions": "Extra cheese please",
        }
      },
      {
        "id": 2,
        "name": "Margherita Pizza",
        "price": "\$16.99",
        "quantity": 1,
        "customization": {
          "size": "Large",
          "crust": "Thin",
        }
      }
    ];

    restaurantData = {
      "id": 1,
      "name": "Bella Vista Italian",
      "headerImage":
          "https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    };

    _calculateTotals();
  }

  void _calculateTotals() {
    subtotal = 0.0;
    for (var item in cartItems) {
      final price = double.parse((item['price'] as String).replaceAll('\$', ''));
      final quantity = item['quantity'] as int;
      subtotal += price * quantity;
    }

    taxes = subtotal * 0.0875; // 8.75% tax rate
    total = subtotal + deliveryFee + serviceFee + taxes + tip - discount;

    setState(() {});
  }

  void _onTipChanged(double newTip) {
    setState(() {
      tip = newTip;
      _calculateTotals();
    });
  }

  void _onTimingChanged(String timing, DateTime? scheduled) {
    setState(() {
      orderTiming = timing;
      scheduledTime = scheduled;
    });
  }

  void _onPaymentMethodSelected(String paymentId) {
    setState(() {
      selectedPaymentId = paymentId;
    });
    HapticFeedback.selectionClick();
  }

  void _changeAddress() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
            height: 70,
            decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
            child: Column(children: [
              Container(
                  padding: EdgeInsets.all(4),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    Text('Select Address', style: AppTheme.lightTheme.textTheme.headlineSmall),
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: CustomIconWidget(
                            iconName: 'close', color: AppTheme.lightTheme.colorScheme.onSurface, size: 24)),
                  ])),
              Expanded(
                  child: Center(
                      child: Text('Address selection interface would go here',
                          style: AppTheme.lightTheme.textTheme.bodyMedium))),
            ])));
  }

  void _addNewCard() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
            height: 80,
            decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
            child: Column(children: [
              Container(
                  padding: EdgeInsets.all(4),
                  child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                    Text('Add New Card', style: AppTheme.lightTheme.textTheme.headlineSmall),
                    IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: CustomIconWidget(
                            iconName: 'close', color: AppTheme.lightTheme.colorScheme.onSurface, size: 24)),
                  ])),
              Expanded(
                  child: Center(
                      child: Text('Credit card form would go here', style: AppTheme.lightTheme.textTheme.bodyMedium))),
            ])));
  }

  Future<void> _placeOrder() async {
    if (selectedPaymentId == null) {
      _showErrorMessage('Please select a payment method');
      return;
    }

    if (orderTiming == 'scheduled' && scheduledTime == null) {
      _showErrorMessage('Please select a delivery time');
      return;
    }

    setState(() {
      isProcessingPayment = true;
    });

    _loadingAnimationController.forward();
    HapticFeedback.mediumImpact();

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 3));

      // Simulate success
      final bool paymentSuccess = true; // In real app, this would be the actual payment result

      if (paymentSuccess) {
        _loadingAnimationController.stop();
        _successAnimationController.forward();

        // Show success dialog
        await _showOrderConfirmation();

        // Navigate to order tracking or home
        if (mounted) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      }
    } catch (error) {
      _loadingAnimationController.reverse();
      _showErrorMessage('Payment failed. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          isProcessingPayment = false;
        });
      }
    }
  }

  Future<void> _showOrderConfirmation() async {
    final orderNumber = 'ORD${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
                content: Column(mainAxisSize: MainAxisSize.min, children: [
                  Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(shape: BoxShape.circle),
                      child: CustomIconWidget(iconName: 'check_circle', size: 48)),
                  SizedBox(height: 2),
                  Text('Order Placed Successfully!',
                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center),
                  SizedBox(height: 1),
                  Text('Order #$orderNumber',
                      style: AppTheme.lightTheme.textTheme.bodyMedium
                          ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                  SizedBox(height: 2),
                  Text(
                      orderTiming == 'asap'
                          ? 'Estimated delivery: 25-35 minutes'
                          : 'Scheduled for: ${scheduledTime != null ? _formatScheduledTime(scheduledTime!) : 'Selected time'}',
                      style: AppTheme.lightTheme.textTheme.bodyMedium,
                      textAlign: TextAlign.center),
                ]),
                actions: [
                  ElevatedButton(onPressed: () => Navigator.of(context).pop(), child: Text('Track Order')),
                ]));
  }

  String _formatScheduledTime(DateTime time) {
    final now = DateTime.now();
    final isToday = time.day == now.day && time.month == now.month && time.year == now.year;
    final isTomorrow =
        time.day == now.add(const Duration(days: 1)).day && time.month == now.month && time.year == now.year;

    String dateStr;
    if (isToday) {
      dateStr = 'Today';
    } else if (isTomorrow) {
      dateStr = 'Tomorrow';
    } else {
      dateStr = '${time.day}/${time.month}/${time.year}';
    }

    final timeStr = TimeOfDay.fromDateTime(time).format(context);
    return '$dateStr at $timeStr';
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.lightTheme.colorScheme.error,
        behavior: SnackBarBehavior.floating));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        appBar: AppBar(
            title: Text('Checkout'),
            leading: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                    iconName: 'arrow_back', color: AppTheme.lightTheme.colorScheme.onSurface, size: 24))),
        body: SingleChildScrollView(
            child: Column(children: [
          SizedBox(height: 1),

          // Delivery address
          DeliveryAddressWidget(currentAddress: currentAddress, onChangeAddress: _changeAddress),

          // Payment method
          PaymentMethodWidget(
              paymentMethods: paymentMethods,
              selectedPaymentId: selectedPaymentId,
              onPaymentMethodSelected: _onPaymentMethodSelected,
              onAddNewCard: _addNewCard),

          // Order timing
          OrderTimingWidget(
              onTimingChanged: _onTimingChanged, initialTiming: orderTiming, initialScheduledTime: scheduledTime),

          // Tip selection
          TipSelectionWidget(orderTotal: subtotal, onTipChanged: _onTipChanged, initialTip: tip),

          // Final order review
          FinalOrderReviewWidget(
              cartItems: cartItems,
              subtotal: subtotal,
              deliveryFee: deliveryFee,
              serviceFee: serviceFee,
              taxes: taxes,
              discount: discount,
              tip: tip,
              total: total,
              promoCode: appliedPromoCode,
              restaurantData: restaurantData),

          SizedBox(height: 12), // Space for bottom button
        ])),
        bottomNavigationBar: _buildPlaceOrderButton());
  }

  Widget _buildPlaceOrderButton() {
    return Container(
        padding: EdgeInsets.fromLTRB(4, 2, 4, 0),
        decoration: BoxDecoration(color: AppTheme.lightTheme.colorScheme.surface, boxShadow: [
          BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 8, offset: const Offset(0, -2)),
        ]),
        child: SafeArea(
            child: Column(mainAxisSize: MainAxisSize.min, children: [
          // Security indicators
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            CustomIconWidget(iconName: 'security', size: 16),
            SizedBox(width: 1),
            Text('SSL Encrypted',
                style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500)),
            SizedBox(width: 4),
            CustomIconWidget(iconName: 'verified_user', size: 16),
            SizedBox(width: 1),
            Text('Secure Payment',
                style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500)),
          ]),
          SizedBox(height: 2),
          SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                  onPressed: isProcessingPayment ? null : _placeOrder,
                  style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 2),
                      backgroundColor: isProcessingPayment
                          ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                          : AppTheme.lightTheme.colorScheme.primary),
                  child: isProcessingPayment
                      ? Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                          SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                  strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white))),
                          SizedBox(width: 3),
                          Text('Processing Payment...',
                              style: AppTheme.lightTheme.textTheme.titleMedium
                                  ?.copyWith(color: Colors.white, fontWeight: FontWeight.w600)),
                        ])
                      : Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                          Text('Place Order',
                              style: AppTheme.lightTheme.textTheme.titleMedium
                                  ?.copyWith(color: Colors.white, fontWeight: FontWeight.w600)),
                          SizedBox(width: 2),
                          Text('\$${total.toStringAsFixed(2)}',
                              style: AppTheme.lightTheme.textTheme.titleMedium
                                  ?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
                        ]))),
          SizedBox(height: 2),
        ])));
  }
}
