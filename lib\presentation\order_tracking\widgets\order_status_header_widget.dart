import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class OrderStatusHeaderWidget extends StatefulWidget {
  final Map<String, dynamic> orderData;
  final Function(String)? onStatusChanged;

  const OrderStatusHeaderWidget({
    super.key,
    required this.orderData,
    this.onStatusChanged,
  });

  @override
  State<OrderStatusHeaderWidget> createState() => _OrderStatusHeaderWidgetState();
}

class _OrderStatusHeaderWidgetState extends State<OrderStatusHeaderWidget> with TickerProviderStateMixin {
  late AnimationController _progressAnimationController;
  late Animation<double> _progressAnimation;

  final List<Map<String, dynamic>> _statusSteps = [
    {
      'key': 'confirmed',
      'title': 'Order Confirmed',
      'icon': 'check_circle',
      'description': 'Your order has been confirmed'
    },
    {'key': 'preparing', 'title': 'Preparing', 'icon': 'restaurant', 'description': 'Chef is preparing your food'},
    {'key': 'ready', 'title': 'Ready for Pickup', 'icon': 'shopping_bag', 'description': 'Order is ready for delivery'},
    {
      'key': 'out_for_delivery',
      'title': 'Out for Delivery',
      'icon': 'delivery_dining',
      'description': 'Driver is on the way'
    },
    {'key': 'delivered', 'title': 'Delivered', 'icon': 'done_all', 'description': 'Order has been delivered'},
  ];

  @override
  void initState() {
    super.initState();
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: _getCurrentProgress(),
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));
    _progressAnimationController.forward();
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    super.dispose();
  }

  double _getCurrentProgress() {
    final currentStatus = widget.orderData['status'] as String;
    final currentIndex = _statusSteps.indexWhere((step) => step['key'] == currentStatus);
    return currentIndex == -1 ? 0.0 : (currentIndex) / (_statusSteps.length - 1);
  }

  bool _isStepCompleted(int stepIndex) {
    final currentStatus = widget.orderData['status'] as String;
    final currentIndex = _statusSteps.indexWhere((step) => step['key'] == currentStatus);
    return stepIndex <= currentIndex;
  }

  bool _isStepActive(int stepIndex) {
    final currentStatus = widget.orderData['status'] as String;
    final currentIndex = _statusSteps.indexWhere((step) => step['key'] == currentStatus);
    return stepIndex == currentIndex;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Info Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order #${widget.orderData['orderNumber']}',
                    style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 0.5),
                  Text(
                    widget.orderData['restaurantName'] as String,
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.primary.withAlpha(26),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  widget.orderData['estimatedTime'] as String,
                  style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 3),

          // Progress Bar
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Container(
                height: 6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  color: AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _progressAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.lightTheme.colorScheme.primary,
                          AppTheme.lightTheme.colorScheme.tertiary,
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 3),

          // Status Steps
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: _statusSteps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isCompleted = _isStepCompleted(index);
              final isActive = _isStepActive(index);

              return Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isCompleted
                            ? (isActive
                                ? AppTheme.lightTheme.colorScheme.primary
                                : AppTheme.lightTheme.colorScheme.tertiary)
                            : AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                        border: isActive
                            ? Border.all(
                                color: AppTheme.lightTheme.colorScheme.primary,
                                width: 2,
                              )
                            : null,
                      ),
                      child: CustomIconWidget(
                        iconName: step['icon'] as String,
                        size: 20,
                        color: isCompleted ? Colors.white : AppTheme.lightTheme.colorScheme.outline,
                      ),
                    ),
                    SizedBox(height: 1),
                    Text(
                      step['title'] as String,
                      textAlign: TextAlign.center,
                      style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                        color: isCompleted
                            ? AppTheme.lightTheme.colorScheme.onSurface
                            : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),

          // Current Status Description
          SizedBox(height: 2),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(3),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _statusSteps.firstWhere(
                (step) => step['key'] == widget.orderData['status'],
                orElse: () => _statusSteps[0],
              )['description'] as String,
              textAlign: TextAlign.center,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
