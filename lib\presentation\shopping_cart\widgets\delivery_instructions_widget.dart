import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class DeliveryInstructionsWidget extends StatefulWidget {
  final Function(String) onInstructionsChanged;
  final String initialInstructions;

  const DeliveryInstructionsWidget({
    super.key,
    required this.onInstructionsChanged,
    this.initialInstructions = '',
  });

  @override
  State<DeliveryInstructionsWidget> createState() => _DeliveryInstructionsWidgetState();
}

class _DeliveryInstructionsWidgetState extends State<DeliveryInstructionsWidget> {
  late TextEditingController _instructionsController;
  bool _isExpanded = false;
  static const int _maxCharacters = 200;

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController(text: widget.initialInstructions);
    _isExpanded = widget.initialInstructions.isNotEmpty;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _onTextChanged(String text) {
    widget.onInstructionsChanged(text);
  }

  @override
  Widget build(BuildContext context) {
    final characterCount = _instructionsController.text.length;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: _toggleExpansion,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(4),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: CustomIconWidget(
                      iconName: 'description',
                      color: AppTheme.lightTheme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  SizedBox(width: 3),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Delivery Instructions',
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          _instructionsController.text.isEmpty
                              ? 'Add special instructions for your driver'
                              : _instructionsController.text.length > 50
                                  ? '${_instructionsController.text.substring(0, 50)}...'
                                  : _instructionsController.text,
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  CustomIconWidget(
                    iconName: _isExpanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),

          // Expandable input section
          if (_isExpanded) ...[
            Divider(
              color: AppTheme.lightTheme.colorScheme.outline,
              height: 1,
            ),
            Padding(
              padding: EdgeInsets.all(4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: _instructionsController,
                    onChanged: _onTextChanged,
                    maxLength: _maxCharacters,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: 'e.g., Leave at door, Ring doorbell, Apartment buzzer code, etc.',
                      contentPadding: EdgeInsets.all(4),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppTheme.lightTheme.colorScheme.outline,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppTheme.lightTheme.colorScheme.outline,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppTheme.lightTheme.colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      counterText: '',
                    ),
                  ),
                  SizedBox(height: 1),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Help your driver find you',
                        style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      Text(
                        '$characterCount/$_maxCharacters',
                        style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                          color: characterCount > _maxCharacters * 0.9
                              ? AppTheme.lightTheme.colorScheme.error
                              : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 2),
                  // Quick suggestions
                  Text(
                    'Quick suggestions:',
                    style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1),
                  Wrap(
                    spacing: 2,
                    runSpacing: 1,
                    children: [
                      'Leave at door',
                      'Ring doorbell',
                      'Call when arrived',
                      'Meet in lobby',
                    ]
                        .map((suggestion) => GestureDetector(
                              onTap: () {
                                _instructionsController.text = suggestion;
                                _onTextChanged(suggestion);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                                decoration: BoxDecoration(
                                  color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: AppTheme.lightTheme.colorScheme.primary.withAlpha(128),
                                  ),
                                ),
                                child: Text(
                                  suggestion,
                                  style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                                    color: AppTheme.lightTheme.colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
