import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class PaymentMethodWidget extends StatelessWidget {
  final List<Map<String, dynamic>> paymentMethods;
  final String? selectedPaymentId;
  final Function(String) onPaymentMethodSelected;
  final VoidCallback onAddNewCard;

  const PaymentMethodWidget({
    super.key,
    required this.paymentMethods,
    this.selectedPaymentId,
    required this.onPaymentMethodSelected,
    required this.onAddNewCard,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Padding(
            padding: EdgeInsets.all(4),
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(children: [
                Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                        borderRadius: BorderRadius.circular(8)),
                    child: CustomIconWidget(
                        iconName: 'payment', color: AppTheme.lightTheme.colorScheme.primary, size: 20)),
                SizedBox(width: 3),
                Text('Payment Method',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
              ]),
              SizedBox(height: 2),

              // Payment methods list
              ...paymentMethods.map((method) => _buildPaymentMethodTile(method)),

              SizedBox(height: 1),

              // Add new card button
              InkWell(
                  onTap: onAddNewCard,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                      padding: EdgeInsets.all(3),
                      decoration: BoxDecoration(
                          border: Border.all(color: AppTheme.lightTheme.colorScheme.outline, style: BorderStyle.solid),
                          borderRadius: BorderRadius.circular(8)),
                      child: Row(children: [
                        Container(
                            padding: EdgeInsets.all(2),
                            decoration: BoxDecoration(
                                color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51), shape: BoxShape.circle),
                            child: CustomIconWidget(
                                iconName: 'add', color: AppTheme.lightTheme.colorScheme.primary, size: 20)),
                        SizedBox(width: 3),
                        Text('Add New Card',
                            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.lightTheme.colorScheme.primary, fontWeight: FontWeight.w500)),
                      ]))),

              SizedBox(height: 2),

              // Security note
              Container(
                  padding: EdgeInsets.all(3),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
                  child: Row(children: [
                    CustomIconWidget(iconName: 'security', size: 16),
                    SizedBox(width: 2),
                    Expanded(
                        child: Text('Your payment information is secured with SSL encryption',
                            style: AppTheme.lightTheme.textTheme.labelSmall
                                ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant))),
                  ])),
            ])));
  }

  Widget _buildPaymentMethodTile(Map<String, dynamic> method) {
    final isSelected = selectedPaymentId == method['id'];

    return Container(
        margin: EdgeInsets.only(bottom: 1),
        decoration: BoxDecoration(
            border: Border.all(
                color: isSelected ? AppTheme.lightTheme.colorScheme.primary : AppTheme.lightTheme.colorScheme.outline,
                width: isSelected ? 2 : 1),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? AppTheme.lightTheme.colorScheme.primary.withAlpha(26) : Colors.transparent),
        child: InkWell(
            onTap: () => onPaymentMethodSelected(method['id']),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
                padding: EdgeInsets.all(3),
                child: Row(children: [
                  // Payment method icon
                  Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                          color: _getPaymentMethodColor(method['type']).withAlpha(51),
                          borderRadius: BorderRadius.circular(8)),
                      child: CustomIconWidget(
                          iconName: _getPaymentMethodIcon(method['type']),
                          color: _getPaymentMethodColor(method['type']),
                          size: 24)),
                  SizedBox(width: 3),

                  // Payment method details
                  Expanded(
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Text(method['name'] as String,
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600)),
                    if (method['details'] != null) ...[
                      SizedBox(height: 0.5),
                      Text(method['details'] as String,
                          style: AppTheme.lightTheme.textTheme.bodySmall
                              ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                    ],
                    if (method['isDefault'] == true) ...[
                      SizedBox(height: 0.5),
                      Container(
                          padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0.5),
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
                          child: Text('Default',
                              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500))),
                    ],
                  ])),

                  // Selection indicator
                  Radio<String>(
                      value: method['id'],
                      groupValue: selectedPaymentId,
                      onChanged: (value) {
                        if (value != null) {
                          onPaymentMethodSelected(value);
                        }
                      },
                      activeColor: AppTheme.lightTheme.colorScheme.primary),
                ]))));
  }

  String _getPaymentMethodIcon(String type) {
    switch (type.toLowerCase()) {
      case 'credit_card':
        return 'credit_card';
      case 'apple_pay':
        return 'apple';
      case 'google_pay':
        return 'google';
      case 'paypal':
        return 'account_balance_wallet';
      default:
        return 'payment';
    }
  }

  Color _getPaymentMethodColor(String type) {
    switch (type.toLowerCase()) {
      case 'credit_card':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'apple_pay':
        return Colors.black;
      case 'google_pay':
        return Colors.blue;
      case 'paypal':
        return Colors.blueAccent;
      default:
        return AppTheme.lightTheme.colorScheme.onSurfaceVariant;
    }
  }
}
