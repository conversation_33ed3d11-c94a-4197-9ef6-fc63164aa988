import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class RestaurantInfoWidget extends StatelessWidget {
  final Map<String, dynamic> restaurantData;

  const RestaurantInfoWidget({
    super.key,
    required this.restaurantData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomImageWidget(
                    imageUrl: restaurantData['headerImage'] as String, width: 15, height: 15, fit: BoxFit.cover)),
            SizedBox(width: 3),
            Expanded(
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(restaurantData['name'] as String,
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600)),
              SizedBox(height: 0.5),
              Text(restaurantData['cuisine'] as String,
                  style: AppTheme.lightTheme.textTheme.bodyMedium
                      ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
            ])),
          ]),
          SizedBox(height: 2),
          Row(children: [
            _buildInfoChip(
                icon: 'star', text: '${restaurantData['rating']}', color: AppTheme.lightTheme.colorScheme.secondary),
            SizedBox(width: 3),
            _buildInfoChip(
                icon: 'access_time',
                text: restaurantData['deliveryTime'] as String,
                color: AppTheme.lightTheme.colorScheme.primary),
            SizedBox(width: 3),
            _buildInfoChip(
                icon: 'attach_money',
                text: restaurantData['minimumOrder'] as String,
                color: AppTheme.lightTheme.colorScheme.tertiary),
          ]),
        ]));
  }

  Widget _buildInfoChip({
    required String icon,
    required String text,
    required Color color,
  }) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0.5),
        decoration: BoxDecoration(color: color.withAlpha(51), borderRadius: BorderRadius.circular(6)),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          CustomIconWidget(iconName: icon, color: color, size: 14),
          SizedBox(width: 1),
          Text(text,
              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(color: color, fontWeight: FontWeight.w600)),
        ]));
  }
}
