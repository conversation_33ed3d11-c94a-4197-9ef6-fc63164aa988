import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/app_export.dart';

class RegistrationFormWidget extends StatelessWidget {
  final TextEditingController fullNameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;

  final FocusNode fullNameFocus;
  final FocusNode emailFocus;
  final FocusNode phoneFocus;
  final FocusNode passwordFocus;
  final FocusNode confirmPasswordFocus;

  final bool isPasswordVisible;
  final bool isConfirmPasswordVisible;
  final bool isFullNameValid;
  final bool isEmailValid;
  final bool isPhoneValid;
  final bool isPasswordValid;
  final bool isConfirmPasswordValid;

  final double passwordStrength;
  final String passwordStrengthText;
  final Color passwordStrengthColor;

  final Function(String) onFullNameChanged;
  final Function(String) onEmailChanged;
  final Function(String) onPhoneChanged;
  final Function(String) onPasswordChanged;
  final Function(String) onConfirmPasswordChanged;
  final VoidCallback onPasswordVisibilityToggle;
  final VoidCallback onConfirmPasswordVisibilityToggle;

  const RegistrationFormWidget({
    super.key,
    required this.fullNameController,
    required this.emailController,
    required this.phoneController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.fullNameFocus,
    required this.emailFocus,
    required this.phoneFocus,
    required this.passwordFocus,
    required this.confirmPasswordFocus,
    required this.isPasswordVisible,
    required this.isConfirmPasswordVisible,
    required this.isFullNameValid,
    required this.isEmailValid,
    required this.isPhoneValid,
    required this.isPasswordValid,
    required this.isConfirmPasswordValid,
    required this.passwordStrength,
    required this.passwordStrengthText,
    required this.passwordStrengthColor,
    required this.onFullNameChanged,
    required this.onEmailChanged,
    required this.onPhoneChanged,
    required this.onPasswordChanged,
    required this.onConfirmPasswordChanged,
    required this.onPasswordVisibilityToggle,
    required this.onConfirmPasswordVisibilityToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Full Name Field
        _buildFormField(
          label: 'Full Name',
          controller: fullNameController,
          focusNode: fullNameFocus,
          nextFocusNode: emailFocus,
          keyboardType: TextInputType.name,
          textInputAction: TextInputAction.next,
          prefixIcon: 'person',
          isValid: isFullNameValid,
          onChanged: onFullNameChanged,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter your full name';
            }
            if (value.trim().length < 2) {
              return 'Name must be at least 2 characters';
            }
            if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim())) {
              return 'Name can only contain letters and spaces';
            }
            return null;
          },
        ),

        SizedBox(height: 2),

        // Email Field
        _buildFormField(
          label: 'Email Address',
          controller: emailController,
          focusNode: emailFocus,
          nextFocusNode: phoneFocus,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          prefixIcon: 'email',
          isValid: isEmailValid,
          onChanged: onEmailChanged,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email address';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        ),

        SizedBox(height: 2),

        // Phone Number Field
        _buildFormField(
          label: 'Phone Number',
          controller: phoneController,
          focusNode: phoneFocus,
          nextFocusNode: passwordFocus,
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.next,
          prefixIcon: 'phone',
          isValid: isPhoneValid,
          onChanged: onPhoneChanged,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(15),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your phone number';
            }
            if (value.length < 10) {
              return 'Phone number must be at least 10 digits';
            }
            return null;
          },
        ),

        SizedBox(height: 2),

        // Password Field
        _buildPasswordField(
          label: 'Password',
          controller: passwordController,
          focusNode: passwordFocus,
          nextFocusNode: confirmPasswordFocus,
          isVisible: isPasswordVisible,
          isValid: isPasswordValid,
          onChanged: onPasswordChanged,
          onVisibilityToggle: onPasswordVisibilityToggle,
          showStrengthIndicator: true,
        ),

        SizedBox(height: 2),

        // Confirm Password Field
        _buildPasswordField(
          label: 'Confirm Password',
          controller: confirmPasswordController,
          focusNode: confirmPasswordFocus,
          isVisible: isConfirmPasswordVisible,
          isValid: isConfirmPasswordValid,
          onChanged: onConfirmPasswordChanged,
          onVisibilityToggle: onConfirmPasswordVisibilityToggle,
          textInputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocusNode,
    required TextInputType keyboardType,
    TextInputAction textInputAction = TextInputAction.next,
    required String prefixIcon,
    required bool isValid,
    required Function(String) onChanged,
    String? Function(String?)? validator,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          onFieldSubmitted: (_) {
            if (nextFocusNode != null) {
              FocusScope.of(focusNode.context!).requestFocus(nextFocusNode);
            }
          },
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Enter your $label',
            prefixIcon: Padding(
              padding: EdgeInsets.all(3),
              child: CustomIconWidget(
                iconName: prefixIcon,
                color: focusNode.hasFocus ? AppTheme.primaryLight : AppTheme.textSecondaryLight,
                size: 5,
              ),
            ),
            suffixIcon: controller.text.isNotEmpty
                ? Padding(
                    padding: EdgeInsets.all(3),
                    child: CustomIconWidget(
                      iconName: isValid ? 'check_circle' : 'error',
                      color: isValid ? AppTheme.successLight : AppTheme.errorLight,
                      size: 5,
                    ),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.dividerLight,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.dividerLight,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.primaryLight,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.errorLight,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.errorLight,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppTheme.lightTheme.colorScheme.surface,
            contentPadding: EdgeInsets.symmetric(
              horizontal: 4,
              vertical: 2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocusNode,
    required bool isVisible,
    required bool isValid,
    required Function(String) onChanged,
    required VoidCallback onVisibilityToggle,
    TextInputAction textInputAction = TextInputAction.next,
    bool showStrengthIndicator = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          obscureText: !isVisible,
          textInputAction: textInputAction,
          onChanged: onChanged,
          onFieldSubmitted: (_) {
            if (nextFocusNode != null) {
              FocusScope.of(focusNode.context!).requestFocus(nextFocusNode);
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your password';
            }
            if (label == 'Confirm Password') {
              if (value != passwordController.text) {
                return 'Passwords do not match';
              }
            } else {
              if (value.length < 8) {
                return 'Password must be at least 8 characters';
              }
            }
            return null;
          },
          decoration: InputDecoration(
            hintText: 'Enter your $label',
            prefixIcon: Padding(
              padding: EdgeInsets.all(3),
              child: CustomIconWidget(
                iconName: 'lock',
                color: focusNode.hasFocus ? AppTheme.primaryLight : AppTheme.textSecondaryLight,
                size: 5,
              ),
            ),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (controller.text.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(right: 2),
                    child: CustomIconWidget(
                      iconName: isValid ? 'check_circle' : 'error',
                      color: isValid ? AppTheme.successLight : AppTheme.errorLight,
                      size: 5,
                    ),
                  ),
                GestureDetector(
                  onTap: onVisibilityToggle,
                  child: Padding(
                    padding: EdgeInsets.all(3),
                    child: CustomIconWidget(
                      iconName: isVisible ? 'visibility' : 'visibility_off',
                      color: AppTheme.textSecondaryLight,
                      size: 5,
                    ),
                  ),
                ),
              ],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.dividerLight,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.dividerLight,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.primaryLight,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.errorLight,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: AppTheme.errorLight,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppTheme.lightTheme.colorScheme.surface,
            contentPadding: EdgeInsets.symmetric(
              horizontal: 4,
              vertical: 2,
            ),
          ),
        ),

        // Password strength indicator
        if (showStrengthIndicator && controller.text.isNotEmpty) ...[
          SizedBox(height: 1),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LinearProgressIndicator(
                value: passwordStrength,
                backgroundColor: AppTheme.dividerLight,
                valueColor: AlwaysStoppedAnimation<Color>(passwordStrengthColor),
                minHeight: 0.5,
              ),
              SizedBox(height: 0.5),
              if (passwordStrengthText.isNotEmpty)
                Text(
                  'Password strength: $passwordStrengthText',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: passwordStrengthColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ],
      ],
    );
  }
}
