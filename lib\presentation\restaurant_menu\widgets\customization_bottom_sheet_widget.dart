import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class CustomizationBottomSheetWidget extends StatefulWidget {
  final Map<String, dynamic> item;
  final Function(Map<String, dynamic>, Map<String, dynamic>) onAddToCart;

  const CustomizationBottomSheetWidget({
    super.key,
    required this.item,
    required this.onAddToCart,
  });

  @override
  State<CustomizationBottomSheetWidget> createState() => _CustomizationBottomSheetWidgetState();
}

class _CustomizationBottomSheetWidgetState extends State<CustomizationBottomSheetWidget> {
  int quantity = 1;
  String selectedSize = 'Regular';
  List<String> selectedAddOns = [];
  String specialInstructions = '';

  final TextEditingController _instructionsController = TextEditingController();

  // Mock customization options
  final List<String> sizes = ['Small', 'Regular', 'Large'];
  final List<Map<String, dynamic>> addOns = [
    {'name': 'Extra Cheese', 'price': 2.99},
    {'name': 'Extra Sauce', 'price': 1.99},
    {'name': 'Garlic Bread', 'price': 3.99},
    {'name': 'Side Salad', 'price': 4.99},
  ];

  double get basePrice {
    final price = (widget.item['price'] as String).replaceAll('\$', '');
    return double.parse(price);
  }

  double get sizeMultiplier {
    switch (selectedSize) {
      case 'Small':
        return 0.8;
      case 'Large':
        return 1.3;
      default:
        return 1.0;
    }
  }

  double get addOnsPrice {
    return selectedAddOns.fold(0.0, (sum, addOnName) {
      final addOn = addOns.firstWhere((a) => a['name'] == addOnName);
      return sum + (addOn['price'] as double);
    });
  }

  double get totalPrice {
    return (basePrice * sizeMultiplier + addOnsPrice) * quantity;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 85,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 2),
            width: 12,
            height: 0.5,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CustomImageWidget(
                    imageUrl: widget.item['image'] as String,
                    width: 20,
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                ),
                SizedBox(width: 4),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.item['name'] as String,
                        style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 1),
                      Text(
                        widget.item['description'] as String,
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Size selection
                  _buildSectionTitle('Size'),
                  SizedBox(height: 1),
                  Wrap(
                    spacing: 2,
                    children: sizes.map((size) => _buildSizeChip(size)).toList(),
                  ),

                  SizedBox(height: 3),

                  // Add-ons
                  _buildSectionTitle('Add-ons'),
                  SizedBox(height: 1),
                  ...addOns.map((addOn) => _buildAddOnTile(addOn)),

                  SizedBox(height: 3),

                  // Special instructions
                  _buildSectionTitle('Special Instructions'),
                  SizedBox(height: 1),
                  TextField(
                    controller: _instructionsController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Any special requests or dietary requirements...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        specialInstructions = value;
                      });
                    },
                  ),

                  SizedBox(height: 3),

                  // Quantity selector
                  _buildSectionTitle('Quantity'),
                  SizedBox(height: 1),
                  Row(
                    children: [
                      _buildQuantityButton(
                        icon: 'remove',
                        onPressed: quantity > 1
                            ? () {
                                setState(() {
                                  quantity--;
                                });
                              }
                            : null,
                      ),
                      SizedBox(width: 4),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppTheme.lightTheme.colorScheme.outline,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          quantity.toString(),
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      SizedBox(width: 4),
                      _buildQuantityButton(
                        icon: 'add',
                        onPressed: () {
                          setState(() {
                            quantity++;
                          });
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 10),
                ],
              ),
            ),
          ),

          // Add to cart button
          Container(
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: AppTheme.lightTheme.colorScheme.shadow,
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    final customization = {
                      'size': selectedSize,
                      'addOns': selectedAddOns,
                      'specialInstructions': specialInstructions,
                      'quantity': quantity,
                      'totalPrice': totalPrice,
                    };

                    widget.onAddToCart(widget.item, customization);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2),
                  ),
                  child: Text(
                    'Add to Cart - \$${totalPrice.toStringAsFixed(2)}',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildSizeChip(String size) {
    final isSelected = selectedSize == size;
    return FilterChip(
      label: Text(size),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          selectedSize = size;
        });
      },
      selectedColor: AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.lightTheme.colorScheme.primary,
      labelStyle: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
        color: isSelected ? AppTheme.lightTheme.colorScheme.primary : AppTheme.lightTheme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
      ),
    );
  }

  Widget _buildAddOnTile(Map<String, dynamic> addOn) {
    final isSelected = selectedAddOns.contains(addOn['name']);
    return CheckboxListTile(
      title: Text(addOn['name'] as String),
      subtitle: Text('+\$${(addOn['price'] as double).toStringAsFixed(2)}'),
      value: isSelected,
      onChanged: (value) {
        setState(() {
          if (value == true) {
            selectedAddOns.add(addOn['name'] as String);
          } else {
            selectedAddOns.remove(addOn['name']);
          }
        });
      },
      controlAffinity: ListTileControlAffinity.trailing,
    );
  }

  Widget _buildQuantityButton({
    required String icon,
    required VoidCallback? onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: onPressed != null ? AppTheme.lightTheme.colorScheme.primary : AppTheme.lightTheme.colorScheme.outline,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: CustomIconWidget(
          iconName: icon,
          color: onPressed != null
              ? AppTheme.lightTheme.colorScheme.primary
              : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
          size: 20,
        ),
      ),
    );
  }
}
