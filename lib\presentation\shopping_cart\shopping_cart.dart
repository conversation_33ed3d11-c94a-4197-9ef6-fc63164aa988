import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/app_export.dart';
import '../restaurant_menu/widgets/customization_bottom_sheet_widget.dart';
import './widgets/cart_item_widget.dart';
import './widgets/delivery_instructions_widget.dart';
import './widgets/order_summary_widget.dart';
import './widgets/promo_code_widget.dart';
import './widgets/restaurant_info_widget.dart';

class ShoppingCart extends StatefulWidget {
  const ShoppingCart({super.key});

  @override
  State<ShoppingCart> createState() => _ShoppingCartState();
}

class _ShoppingCartState extends State<ShoppingCart> with TickerProviderStateMixin {
  late AnimationController _slideAnimationController;

  // Mock restaurant data
  final Map<String, dynamic> restaurantData = {
    "id": 1,
    "name": "Bella Vista Italian",
    "rating": 4.8,
    "deliveryTime": "25-35 min",
    "minimumOrder": "\$15.00",
    "headerImage":
        "https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    "cuisine": "Italian • Mediterranean",
    "distance": "1.2 km away",
    "deliveryFee": "\$2.99"
  };

  // Mock cart items
  List<Map<String, dynamic>> cartItems = [
    {
      "id": 1,
      "name": "Spaghetti Carbonara",
      "description": "Classic Roman pasta with eggs, pancetta, parmesan cheese, and black pepper",
      "price": "\$18.99",
      "category": "Mains",
      "image":
          "https://images.pexels.com/photos/4518843/pexels-photo-4518843.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "quantity": 2,
      "customization": {
        "size": "Regular",
        "spiceLevel": "Medium",
        "specialInstructions": "Extra cheese please",
        "quantity": 2
      }
    },
    {
      "id": 2,
      "name": "Margherita Pizza",
      "description": "Traditional Neapolitan pizza with fresh mozzarella, tomato sauce, and basil",
      "price": "\$16.99",
      "category": "Mains",
      "image":
          "https://images.pexels.com/photos/315755/pexels-photo-315755.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "quantity": 1,
      "customization": {"size": "Large", "crust": "Thin", "quantity": 1}
    },
    {
      "id": 3,
      "name": "Tiramisu",
      "description": "Classic Italian dessert with coffee-soaked ladyfingers and mascarpone cream",
      "price": "\$8.99",
      "category": "Desserts",
      "image":
          "https://images.pexels.com/photos/6880219/pexels-photo-6880219.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "quantity": 1,
      "customization": {"quantity": 1}
    }
  ];

  // Order calculation variables
  double subtotal = 0.0;
  double deliveryFee = 2.99;
  double serviceFee = 1.50;
  double taxes = 0.0;
  double discount = 0.0;
  double total = 0.0;
  String? appliedPromoCode;
  String deliveryInstructions = '';
  bool isPromoLoading = false;

  // Constants
  static const double minimumOrderAmount = 15.0;
  static const double taxRate = 0.0875; // 8.75%

  @override
  void initState() {
    super.initState();
    _slideAnimationController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _calculateTotals();
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    super.dispose();
  }

  void _calculateTotals() {
    subtotal = 0.0;
    for (var item in cartItems) {
      final price = double.parse((item['price'] as String).replaceAll('\$', ''));
      final quantity = item['quantity'] as int;
      subtotal += price * quantity;
    }

    taxes = subtotal * taxRate;
    total = subtotal + deliveryFee + serviceFee + taxes - discount;

    setState(() {});
  }

  void _updateItemQuantity(int itemId, int newQuantity) {
    setState(() {
      final itemIndex = cartItems.indexWhere((item) => item['id'] == itemId);
      if (itemIndex != -1) {
        cartItems[itemIndex]['quantity'] = newQuantity;
        cartItems[itemIndex]['customization']['quantity'] = newQuantity;
        _calculateTotals();
        HapticFeedback.lightImpact();
      }
    });
  }

  void _deleteItem(int itemId) {
    setState(() {
      cartItems.removeWhere((item) => item['id'] == itemId);
      _calculateTotals();
      HapticFeedback.mediumImpact();
    });
  }

  void _editItem(Map<String, dynamic> item) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => CustomizationBottomSheetWidget(
            item: item,
            onAddToCart: (updatedItem, customization) {
              setState(() {
                final itemIndex = cartItems.indexWhere((cartItem) => cartItem['id'] == item['id']);
                if (itemIndex != -1) {
                  cartItems[itemIndex]['customization'] = customization;
                  cartItems[itemIndex]['quantity'] = customization['quantity'];
                  _calculateTotals();
                }
              });
            }));
  }

  void _applyPromoCode(String promoCode) async {
    setState(() {
      isPromoLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      isPromoLoading = false;
      // Mock promo code validation
      if (promoCode.toUpperCase() == 'SAVE10') {
        appliedPromoCode = promoCode.toUpperCase();
        discount = subtotal * 0.1; // 10% discount
        _calculateTotals();
        _showSuccessMessage('Promo code applied! You saved \${discount.toStringAsFixed(2)}');
      } else if (promoCode.toUpperCase() == 'FREEDELIV') {
        appliedPromoCode = promoCode.toUpperCase();
        discount = deliveryFee;
        _calculateTotals();
        _showSuccessMessage('Free delivery applied!');
      } else if (promoCode.toUpperCase() == 'FIRST20') {
        appliedPromoCode = promoCode.toUpperCase();
        discount = subtotal * 0.2; // 20% discount
        _calculateTotals();
        _showSuccessMessage('Welcome! 20% off applied!');
      } else {
        _showErrorMessage('Invalid promo code. Please try again.');
      }
    });
  }

  void _removePromoCode() {
    setState(() {
      appliedPromoCode = null;
      discount = 0.0;
      _calculateTotals();
    });
    _showSuccessMessage('Promo code removed');
  }

  void _updateDeliveryInstructions(String instructions) {
    deliveryInstructions = instructions;
  }

  void _proceedToCheckout() {
    if (subtotal < minimumOrderAmount) {
      _showErrorMessage('Minimum order of \${minimumOrderAmount.toStringAsFixed(2)} required');
      return;
    }

    HapticFeedback.mediumImpact();

    // Navigate to checkout
    Navigator.pushNamed(context, AppRoutes.checkout, arguments: {
      'cartItems': cartItems,
      'subtotal': subtotal,
      'deliveryFee': deliveryFee,
      'serviceFee': serviceFee,
      'taxes': taxes,
      'discount': discount,
      'total': total,
      'appliedPromoCode': appliedPromoCode,
      'deliveryInstructions': deliveryInstructions,
      'restaurantData': restaurantData,
    });
  }

  void _showContinueShoppingSuggestions() {
    showModalBottomSheet(
        context: context,
        builder: (context) => Container(
            padding: EdgeInsets.all(4),
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Text('Continue Shopping',
                  style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600)),
              SizedBox(height: 2),
              Text('Discover more delicious items from ${restaurantData['name']}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium, textAlign: TextAlign.center),
              SizedBox(height: 3),
              SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.pop(context); // Go back to restaurant menu
                      },
                      child: Text('Browse Menu'))),
              SizedBox(height: 1),
              SizedBox(
                  width: double.infinity,
                  child: TextButton(onPressed: () => Navigator.pop(context), child: Text('Stay in Cart'))),
            ])));
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), behavior: SnackBarBehavior.floating));
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.lightTheme.colorScheme.error,
        behavior: SnackBarBehavior.floating));
  }

  @override
  Widget build(BuildContext context) {
    final bool isMinimumOrderMet = subtotal >= minimumOrderAmount;

    return Scaffold(
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        appBar: AppBar(
            title: Text('Shopping Cart'),
            leading: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                    iconName: 'arrow_back', color: AppTheme.lightTheme.colorScheme.onSurface, size: 24)),
            actions: [
              if (cartItems.isNotEmpty)
                IconButton(
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                                  title: Text('Clear Cart'),
                                  content: Text('Are you sure you want to remove all items from your cart?'),
                                  actions: [
                                    TextButton(onPressed: () => Navigator.pop(context), child: Text('Cancel')),
                                    ElevatedButton(
                                        onPressed: () {
                                          Navigator.pop(context);
                                          setState(() {
                                            cartItems.clear();
                                            _calculateTotals();
                                          });
                                        },
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor: AppTheme.lightTheme.colorScheme.error),
                                        child: Text('Clear All')),
                                  ]));
                    },
                    icon: CustomIconWidget(
                        iconName: 'delete_sweep', color: AppTheme.lightTheme.colorScheme.error, size: 24)),
            ]),
        body: cartItems.isEmpty ? _buildEmptyCartState() : _buildCartContent(),
        bottomNavigationBar: cartItems.isNotEmpty ? _buildCheckoutButton(isMinimumOrderMet) : null);
  }

  Widget _buildEmptyCartState() {
    return Center(
        child: Padding(
            padding: EdgeInsets.all(6),
            child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              CustomIconWidget(
                  iconName: 'shopping_cart_outlined',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 80),
              SizedBox(height: 3),
              Text('Your cart is empty',
                  style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.w600)),
              SizedBox(height: 1),
              Text('Add items from ${restaurantData['name']} to get started',
                  style: AppTheme.lightTheme.textTheme.bodyLarge
                      ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant),
                  textAlign: TextAlign.center),
              SizedBox(height: 4),
              SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(onPressed: _showContinueShoppingSuggestions, child: Text('Continue Shopping'))),
              SizedBox(height: 2),
              // Restaurant suggestions
              Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                            color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
                      ]),
                  child: Column(children: [
                    Text('Popular items from ${restaurantData['name']}:',
                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
                    SizedBox(height: 2),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          'Carbonara',
                          'Margherita',
                          'Tiramisu',
                        ]
                            .map((item) => Chip(
                                label: Text(item),
                                backgroundColor: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                                side: BorderSide.none))
                            .toList()),
                  ])),
            ])));
  }

  Widget _buildCartContent() {
    final bool isMinimumOrderMet = subtotal >= minimumOrderAmount;

    return SingleChildScrollView(
        child: Column(children: [
      // Restaurant info
      RestaurantInfoWidget(restaurantData: restaurantData),

      SizedBox(height: 2),

      // Cart items
      ...cartItems.map((item) => CartItemWidget(
          item: item,
          onEdit: () => _editItem(item),
          onDelete: () => _deleteItem(item['id']),
          onQuantityChanged: (newQuantity) => _updateItemQuantity(item['id'], newQuantity))),

      SizedBox(height: 2),

      // Promo code section
      PromoCodeWidget(
          onPromoApplied: _applyPromoCode,
          onPromoRemoved: appliedPromoCode != null ? _removePromoCode : null,
          appliedPromoCode: appliedPromoCode,
          isLoading: isPromoLoading),

      SizedBox(height: 2),

      // Delivery instructions
      DeliveryInstructionsWidget(
          onInstructionsChanged: _updateDeliveryInstructions, initialInstructions: deliveryInstructions),

      SizedBox(height: 2),

      // Order summary
      OrderSummaryWidget(
          subtotal: subtotal,
          deliveryFee: deliveryFee,
          serviceFee: serviceFee,
          taxes: taxes,
          discount: discount,
          total: total,
          promoCode: appliedPromoCode,
          showMinimumOrderWarning: !isMinimumOrderMet,
          minimumOrderAmount: minimumOrderAmount),

      SizedBox(height: 12), // Space for bottom button
    ]));
  }

  Widget _buildCheckoutButton(bool isMinimumOrderMet) {
    return Container(
        padding: EdgeInsets.fromLTRB(4, 2, 4, 0),
        decoration: BoxDecoration(color: AppTheme.lightTheme.colorScheme.surface, boxShadow: [
          BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 8, offset: const Offset(0, -2)),
        ]),
        child: SafeArea(
            child: Column(mainAxisSize: MainAxisSize.min, children: [
          if (!isMinimumOrderMet) ...[
            Container(
                padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
                child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  CustomIconWidget(iconName: 'warning', size: 16),
                  SizedBox(width: 2),
                  Text('Add \${(minimumOrderAmount - subtotal).toStringAsFixed(2)} more to proceed',
                      style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant, fontWeight: FontWeight.w500)),
                ])),
            SizedBox(height: 2),
          ],
          SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                  onPressed: isMinimumOrderMet ? _proceedToCheckout : null,
                  style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 2),
                      backgroundColor: isMinimumOrderMet
                          ? AppTheme.lightTheme.colorScheme.primary
                          : AppTheme.lightTheme.colorScheme.onSurfaceVariant),
                  child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                    Text('Proceed to Checkout',
                        style: AppTheme.lightTheme.textTheme.titleMedium
                            ?.copyWith(color: Colors.white, fontWeight: FontWeight.w600)),
                    SizedBox(width: 2),
                    Text('\${total.toStringAsFixed(2)}',
                        style: AppTheme.lightTheme.textTheme.titleMedium
                            ?.copyWith(color: Colors.white, fontWeight: FontWeight.w700)),
                  ]))),
          SizedBox(height: 2),
        ])));
  }
}
