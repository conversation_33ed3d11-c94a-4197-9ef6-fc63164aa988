import 'package:flutter/material.dart';

class ActivityFeedWidget extends StatelessWidget {
  const ActivityFeedWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final activities = _getMockActivities();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                TextButton(
                  onPressed: () {},
                  child: Text('View All'),
                ),
              ],
            ),
            SizedBox(height: 16),
            ...activities.take(5).map((activity) => _buildActivityItem(context, activity)),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, ActivityModel activity) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getActivityColor(activity.type).withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity.type),
              size: 20,
              color: _getActivityColor(activity.type),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                SizedBox(height: 2),
                Text(
                  activity.subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
          Text(
            activity.timestamp,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(ActivityType type) {
    switch (type) {
      case ActivityType.newRegistration:
        return Icons.restaurant_outlined;
      case ActivityType.orderComplete:
        return Icons.check_circle_outline;
      case ActivityType.supportTicket:
        return Icons.support_agent_outlined;
      case ActivityType.systemAlert:
        return Icons.warning_amber_outlined;
    }
  }

  Color _getActivityColor(ActivityType type) {
    switch (type) {
      case ActivityType.newRegistration:
        return Colors.blue;
      case ActivityType.orderComplete:
        return Colors.green;
      case ActivityType.supportTicket:
        return Colors.orange;
      case ActivityType.systemAlert:
        return Colors.red;
    }
  }

  List<ActivityModel> _getMockActivities() {
    return [
      ActivityModel(
        title: 'New Restaurant Registration',
        subtitle: 'Pizza Palace submitted application',
        timestamp: '2 min ago',
        type: ActivityType.newRegistration,
      ),
      ActivityModel(
        title: 'Order Completed',
        subtitle: 'Order #12345 delivered successfully',
        timestamp: '5 min ago',
        type: ActivityType.orderComplete,
      ),
      ActivityModel(
        title: 'Support Ticket',
        subtitle: 'High priority ticket from Burger King',
        timestamp: '15 min ago',
        type: ActivityType.supportTicket,
      ),
      ActivityModel(
        title: 'System Alert',
        subtitle: 'Server response time increased',
        timestamp: '30 min ago',
        type: ActivityType.systemAlert,
      ),
      ActivityModel(
        title: 'Order Completed',
        subtitle: 'Order #12344 delivered successfully',
        timestamp: '45 min ago',
        type: ActivityType.orderComplete,
      ),
    ];
  }
}

class ActivityModel {
  final String title;
  final String subtitle;
  final String timestamp;
  final ActivityType type;

  ActivityModel({
    required this.title,
    required this.subtitle,
    required this.timestamp,
    required this.type,
  });
}

enum ActivityType {
  newRegistration,
  orderComplete,
  supportTicket,
  systemAlert,
}
