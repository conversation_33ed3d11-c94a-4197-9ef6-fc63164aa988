import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/pos_alerts_widget.dart';
import './widgets/pos_comparative_analysis_widget.dart';
import './widgets/pos_customer_insights_widget.dart';
import './widgets/pos_date_range_selector_widget.dart';
import './widgets/pos_heat_map_widget.dart';
import './widgets/pos_kpi_section_widget.dart';
import './widgets/pos_menu_analytics_widget.dart';
import './widgets/pos_real_time_monitoring_widget.dart';
import './widgets/pos_sales_chart_widget.dart';

class PosAnalyticsDashboard extends StatefulWidget {
  const PosAnalyticsDashboard({super.key});

  @override
  State<PosAnalyticsDashboard> createState() => _PosAnalyticsDashboardState();
}

class _PosAnalyticsDashboardState extends State<PosAnalyticsDashboard> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String selectedDateRange = 'Today';
  bool showFilters = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'POS Analytics Dashboard',
        style: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize: 20,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.notifications_outlined),
          onPressed: () {
            _showAlertsDialog();
          },
          tooltip: 'Alerts',
        ),
        IconButton(
          icon: Icon(
            showFilters ? Icons.filter_list : Icons.filter_list_outlined,
            color: showFilters ? Theme.of(context).colorScheme.primary : null,
          ),
          onPressed: () {
            setState(() {
              showFilters = !showFilters;
            });
          },
          tooltip: 'Toggle Filters',
        ),
        IconButton(
          icon: Icon(Icons.refresh),
          onPressed: () {
            _refreshData();
          },
          tooltip: 'Refresh Data',
        ),
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'export':
                _showExportDialog();
                break;
              case 'settings':
                _showSettingsDialog();
                break;
              case 'help':
                _showHelpDialog();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 18),
                  SizedBox(width: 8),
                  Text('Export Report'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, size: 18),
                  SizedBox(width: 8),
                  Text('Settings'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, size: 18),
                  SizedBox(width: 8),
                  Text('Help'),
                ],
              ),
            ),
          ],
        ),
        SizedBox(width: 8),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: [
          Tab(
            icon: Icon(Icons.dashboard, size: 20),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.trending_up, size: 20),
            text: 'Sales',
          ),
          Tab(
            icon: Icon(Icons.restaurant_menu, size: 20),
            text: 'Menu',
          ),
          Tab(
            icon: Icon(Icons.schedule, size: 20),
            text: 'Performance',
          ),
          Tab(
            icon: Icon(Icons.people, size: 20),
            text: 'Customers',
          ),
        ],
      ),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Text(
                    'POS',
                    style: GoogleFonts.inter(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'RestaurantHub POS',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Analytics Dashboard',
                  style: GoogleFonts.inter(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(Icons.dashboard, 'Dashboard', () {
            Navigator.pushNamed(context, AppRoutes.adminDashboard);
          }),
          _buildDrawerItem(Icons.analytics, 'POS Analytics', () {}, isSelected: true),
          _buildDrawerItem(Icons.point_of_sale, 'Order Management', () {
            Navigator.pushNamed(context, AppRoutes.posOrderManagement);
          }),
          _buildDrawerItem(Icons.menu_book, 'Menu Management', () {
            Navigator.pushNamed(context, AppRoutes.posMenuManagement);
          }),
          _buildDrawerItem(Icons.restaurant, 'Restaurant Management', () {
            Navigator.pushNamed(context, AppRoutes.restaurantManagement);
          }),
          _buildDrawerItem(Icons.bar_chart, 'Order Analytics', () {
            Navigator.pushNamed(context, AppRoutes.orderAnalytics);
          }),
          _buildDrawerItem(Icons.people, 'Customer Management', () {}),
          _buildDrawerItem(Icons.settings, 'Settings', () {}),
          Divider(),
          _buildDrawerItem(Icons.logout, 'Logout', () {}),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap, {bool isSelected = false}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).colorScheme.primary.withAlpha(26) : null,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
        ),
        title: Text(
          title,
          style: GoogleFonts.inter(
            color: isSelected ? Theme.of(context).colorScheme.primary : null,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        if (showFilters)
          PosDateRangeSelectorWidget(
            selectedDateRange: selectedDateRange,
            onDateRangeChanged: (range) {
              setState(() {
                selectedDateRange = range;
              });
            },
          ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildSalesTab(),
              _buildMenuTab(),
              _buildPerformanceTab(),
              _buildCustomersTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PosKpiSectionWidget(dateRange: selectedDateRange),
          SizedBox(height: 24),
          PosRealTimeMonitoringWidget(),
          SizedBox(height: 24),
          PosAlertsWidget(),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: PosSalesChartWidget(
                  title: 'Today\'s Sales',
                  chartType: 'line',
                  height: 200,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: PosSalesChartWidget(
                  title: 'Order Types',
                  chartType: 'pie',
                  height: 200,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSalesTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          PosSalesChartWidget(
            title: 'Sales Trends',
            chartType: 'line',
            height: 250,
          ),
          SizedBox(height: 24),
          PosHeatMapWidget(),
          SizedBox(height: 24),
          PosComparativeAnalysisWidget(),
        ],
      ),
    );
  }

  Widget _buildMenuTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          PosMenuAnalyticsWidget(),
          SizedBox(height: 24),
          PosSalesChartWidget(
            title: 'Menu Performance',
            chartType: 'bar',
            height: 250,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          PosHeatMapWidget(),
          SizedBox(height: 24),
          PosRealTimeMonitoringWidget(),
          SizedBox(height: 24),
          PosSalesChartWidget(
            title: 'Kitchen Efficiency',
            chartType: 'line',
            height: 250,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          PosCustomerInsightsWidget(),
          SizedBox(height: 24),
          PosSalesChartWidget(
            title: 'Customer Trends',
            chartType: 'line',
            height: 250,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        _showExportDialog();
      },
      icon: Icon(Icons.download),
      label: Text('Export'),
      backgroundColor: Theme.of(context).colorScheme.primary,
    );
  }

  void _refreshData() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Refreshing analytics data...')),
    );
  }

  void _showAlertsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'System Alerts',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              SizedBox(height: 16),
              PosAlertsWidget(),
              SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Close'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Export Report'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Select export format:'),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    _exportReport('PDF');
                  },
                  icon: Icon(Icons.picture_as_pdf),
                  label: Text('PDF'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    _exportReport('Excel');
                  },
                  icon: Icon(Icons.table_chart),
                  label: Text('Excel'),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _exportReport(String format) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exporting report as $format...')),
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Analytics Settings'),
        content: Text('Configure analytics preferences and display options.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('POS Analytics Help'),
        content: Text(
          'This dashboard provides comprehensive POS analytics including:\n\n'
          '• Real-time sales monitoring\n'
          '• Menu performance analysis\n'
          '• Customer behavior insights\n'
          '• Peak hour heat maps\n'
          '• Kitchen efficiency metrics\n'
          '• Comparative analysis tools\n\n'
          'Use the date range selector to customize your view and export reports for detailed analysis.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Got it'),
          ),
        ],
      ),
    );
  }
}
