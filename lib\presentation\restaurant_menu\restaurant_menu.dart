import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/cart_summary_widget.dart';
import './widgets/category_tabs_widget.dart';
import './widgets/customization_bottom_sheet_widget.dart';
import './widgets/menu_item_card_widget.dart';
import './widgets/restaurant_header_widget.dart';

class RestaurantMenu extends StatefulWidget {
  const RestaurantMenu({super.key});

  @override
  State<RestaurantMenu> createState() => _RestaurantMenuState();
}

class _RestaurantMenuState extends State<RestaurantMenu> with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late TabController _tabController;
  late AnimationController _cartAnimationController;

  bool _isSearchVisible = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Mock restaurant data
  final Map<String, dynamic> restaurantData = {
    "id": 1,
    "name": "Bella Vista Italian",
    "rating": 4.8,
    "deliveryTime": "25-35 min",
    "minimumOrder": "\$15.00",
    "headerImage":
        "https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    "cuisine": "Italian • Mediterranean",
    "distance": "1.2 km away",
    "deliveryFee": "\$2.99"
  };

  // Mock menu categories
  final List<String> categories = ["Appetizers", "Mains", "Desserts", "Drinks"];

  // Mock menu items
  final List<Map<String, dynamic>> menuItems = [
    {
      "id": 1,
      "name": "Bruschetta Trio",
      "description": "Three varieties of our signature bruschetta with fresh tomatoes, basil, and mozzarella",
      "price": "\$12.99",
      "category": "Appetizers",
      "image":
          "https://images.pexels.com/photos/1438672/pexels-photo-1438672.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": false,
      "isSpicy": false,
      "rating": 4.7,
      "preparationTime": "10-15 min"
    },
    {
      "id": 2,
      "name": "Calamari Fritti",
      "description": "Crispy fried squid rings served with marinara sauce and lemon wedges",
      "price": "\$14.99",
      "category": "Appetizers",
      "image":
          "https://images.pexels.com/photos/725991/pexels-photo-725991.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": false,
      "isGlutenFree": false,
      "isSpicy": false,
      "rating": 4.5,
      "preparationTime": "12-18 min"
    },
    {
      "id": 3,
      "name": "Spaghetti Carbonara",
      "description": "Classic Roman pasta with eggs, pancetta, parmesan cheese, and black pepper",
      "price": "\$18.99",
      "category": "Mains",
      "image":
          "https://images.pexels.com/photos/4518843/pexels-photo-4518843.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": false,
      "isGlutenFree": false,
      "isSpicy": false,
      "rating": 4.9,
      "preparationTime": "15-20 min"
    },
    {
      "id": 4,
      "name": "Margherita Pizza",
      "description": "Traditional Neapolitan pizza with fresh mozzarella, tomato sauce, and basil",
      "price": "\$16.99",
      "category": "Mains",
      "image":
          "https://images.pexels.com/photos/315755/pexels-photo-315755.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": false,
      "isSpicy": false,
      "rating": 4.6,
      "preparationTime": "18-25 min"
    },
    {
      "id": 5,
      "name": "Tiramisu",
      "description": "Classic Italian dessert with coffee-soaked ladyfingers and mascarpone cream",
      "price": "\$8.99",
      "category": "Desserts",
      "image":
          "https://images.pexels.com/photos/6880219/pexels-photo-6880219.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": false,
      "isSpicy": false,
      "rating": 4.8,
      "preparationTime": "5 min"
    },
    {
      "id": 6,
      "name": "Panna Cotta",
      "description": "Silky smooth vanilla custard topped with fresh berry compote",
      "price": "\$7.99",
      "category": "Desserts",
      "image":
          "https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": true,
      "isSpicy": false,
      "rating": 4.4,
      "preparationTime": "3 min"
    },
    {
      "id": 7,
      "name": "Italian Soda",
      "description": "Refreshing sparkling water with your choice of fruit syrup",
      "price": "\$3.99",
      "category": "Drinks",
      "image":
          "https://images.pexels.com/photos/1283219/pexels-photo-1283219.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": true,
      "isSpicy": false,
      "rating": 4.2,
      "preparationTime": "2 min"
    },
    {
      "id": 8,
      "name": "Espresso",
      "description": "Rich and bold Italian espresso served in traditional ceramic cup",
      "price": "\$2.99",
      "category": "Drinks",
      "image":
          "https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
      "isVegetarian": true,
      "isGlutenFree": true,
      "isSpicy": false,
      "rating": 4.7,
      "preparationTime": "3 min"
    }
  ];

  // Cart state
  List<Map<String, dynamic>> cartItems = [];
  double cartTotal = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _tabController = TabController(length: categories.length, vsync: this);
    _cartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    _cartAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get filteredMenuItems {
    if (_searchQuery.isEmpty) {
      return menuItems;
    }
    return menuItems.where((item) {
      final name = (item['name'] as String).toLowerCase();
      final description = (item['description'] as String).toLowerCase();
      final query = _searchQuery.toLowerCase();
      return name.contains(query) || description.contains(query);
    }).toList();
  }

  List<Map<String, dynamic>> getItemsByCategory(String category) {
    return filteredMenuItems.where((item) => item['category'] == category).toList();
  }

  void _addToCart(Map<String, dynamic> item, Map<String, dynamic> customization) {
    setState(() {
      final existingIndex = cartItems.indexWhere((cartItem) =>
          cartItem['id'] == item['id'] && cartItem['customization'].toString() == customization.toString());

      if (existingIndex != -1) {
        cartItems[existingIndex]['quantity'] += customization['quantity'] as int;
      } else {
        cartItems.add({
          ...item,
          'quantity': customization['quantity'],
          'customization': customization,
        });
      }

      _updateCartTotal();
      _cartAnimationController.forward().then((_) {
        _cartAnimationController.reverse();
      });
    });
  }

  void _updateCartTotal() {
    cartTotal = 0.0;
    for (var item in cartItems) {
      final price = double.parse((item['price'] as String).replaceAll('\$', ''));
      final quantity = item['quantity'] as int;
      cartTotal += price * quantity;
    }
  }

  void _showCustomizationSheet(Map<String, dynamic> item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CustomizationBottomSheetWidget(
        item: item,
        onAddToCart: _addToCart,
      ),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _searchQuery = '';
        _searchController.clear();
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.delayed(const Duration(seconds: 1));
        },
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverAppBar(
              expandedHeight: 30,
              floating: false,
              pinned: true,
              backgroundColor: AppTheme.lightTheme.colorScheme.surface,
              leading: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'arrow_back',
                  color: AppTheme.lightTheme.colorScheme.onSurface,
                  size: 24,
                ),
              ),
              actions: [
                IconButton(
                  onPressed: _toggleSearch,
                  icon: CustomIconWidget(
                    iconName: _isSearchVisible ? 'close' : 'search',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: CustomIconWidget(
                    iconName: 'favorite_border',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: CustomIconWidget(
                    iconName: 'share',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: RestaurantHeaderWidget(
                  restaurantData: restaurantData,
                ),
              ),
            ),
            if (_isSearchVisible)
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.all(4),
                  color: AppTheme.lightTheme.colorScheme.surface,
                  child: TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    autofocus: true,
                    decoration: InputDecoration(
                      hintText: 'Search menu items...',
                      prefixIcon: CustomIconWidget(
                        iconName: 'search',
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              onPressed: () {
                                _searchController.clear();
                                _onSearchChanged('');
                              },
                              icon: CustomIconWidget(
                                iconName: 'clear',
                                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                size: 20,
                              ),
                            )
                          : null,
                    ),
                  ),
                ),
              ),
            SliverPersistentHeader(
              pinned: true,
              delegate: _SliverAppBarDelegate(
                child: CategoryTabsWidget(
                  categories: categories,
                  tabController: _tabController,
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final category = categories[index];
                  final categoryItems = getItemsByCategory(category);

                  if (categoryItems.isEmpty && _searchQuery.isNotEmpty) {
                    return const SizedBox.shrink();
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(4, 3, 4, 1),
                        child: Text(
                          category,
                          style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (categoryItems.isEmpty)
                        Container(
                          padding: EdgeInsets.all(4),
                          child: Text(
                            'No items found in this category',
                            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        )
                      else
                        ...categoryItems.map((item) => MenuItemCardWidget(
                              item: item,
                              onTap: () => _showCustomizationSheet(item),
                              onLongPress: () => _showQuickActions(item),
                              searchQuery: _searchQuery,
                              isInCart: cartItems.any((cartItem) => cartItem['id'] == item['id']),
                            )),
                      SizedBox(height: 2),
                    ],
                  );
                },
                childCount: categories.length,
              ),
            ),
            SliverToBoxAdapter(
              child: SizedBox(height: 15),
            ),
          ],
        ),
      ),
      bottomSheet: cartItems.isNotEmpty
          ? CartSummaryWidget(
              cartItems: cartItems,
              cartTotal: cartTotal,
              onViewCart: () => _showFullCart(),
              animationController: _cartAnimationController,
            )
          : null,
    );
  }

  void _showQuickActions(Map<String, dynamic> item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CustomIconWidget(
                iconName: 'favorite_border',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Add to Favorites'),
              onTap: () {
                Navigator.pop(context);
                // Add to favorites logic
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'share',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('Share Item'),
              onTap: () {
                Navigator.pop(context);
                // Share item logic
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'info_outline',
                color: AppTheme.lightTheme.colorScheme.primary,
                size: 24,
              ),
              title: Text('View Nutrition'),
              onTap: () {
                Navigator.pop(context);
                // View nutrition logic
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFullCart() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 80,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Your Cart',
                    style: AppTheme.lightTheme.textTheme.headlineSmall,
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: CustomIconWidget(
                      iconName: 'close',
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: cartItems.length,
                itemBuilder: (context, index) {
                  final item = cartItems[index];
                  return ListTile(
                    leading: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CustomImageWidget(
                        imageUrl: item['image'] as String,
                        width: 15,
                        height: 15,
                        fit: BoxFit.cover,
                      ),
                    ),
                    title: Text(item['name'] as String),
                    subtitle: Text('Quantity: ${item['quantity']}'),
                    trailing: Text(
                      item['price'] as String,
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              padding: EdgeInsets.all(4),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total',
                        style: AppTheme.lightTheme.textTheme.titleLarge,
                      ),
                      Text(
                        '\$${cartTotal.toStringAsFixed(2)}',
                        style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.lightTheme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 2),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Proceed to checkout
                      },
                      child: Text('Proceed to Checkout'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _SliverAppBarDelegate({required this.child});

  @override
  double get minExtent => 8;

  @override
  double get maxExtent => 8;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
