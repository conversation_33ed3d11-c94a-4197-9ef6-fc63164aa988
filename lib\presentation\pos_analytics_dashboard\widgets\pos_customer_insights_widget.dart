import 'package:flutter/material.dart';

class PosCustomerInsightsWidget extends StatelessWidget {
  const PosCustomerInsightsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Behavior Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            _buildInsightSection(
              'Repeat Customers',
              Icons.repeat,
              Colors.blue,
              '68%',
              'of customers return within 30 days',
            ),
            SizedBox(height: 12),
            _buildInsightSection(
              'Preferred Items',
              Icons.favorite,
              Colors.red,
              'Pizza & Pasta',
              'most ordered category',
            ),
            SizedBox(height: 12),
            _buildInsightSection(
              'Seasonal Trends',
              Icons.calendar_today,
              Colors.green,
              'Summer Salads',
              'trending +45% this month',
            ),
            SizedBox(height: 16),
            _buildCustomerSegments(),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightSection(String title, IconData icon, Color color, String metric, String description) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2),
              RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  children: [
                    TextSpan(
                      text: metric,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                    TextSpan(text: ' $description'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerSegments() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customer Segments',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8),
        _buildSegmentBar('Regular Customers', 0.4, Colors.blue),
        SizedBox(height: 4),
        _buildSegmentBar('Occasional Visitors', 0.35, Colors.orange),
        SizedBox(height: 4),
        _buildSegmentBar('First-time Customers', 0.25, Colors.green),
      ],
    );
  }

  Widget _buildSegmentBar(String label, double percentage, Color color) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(width: 8),
        Expanded(
          child: Stack(
            children: [
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              FractionallySizedBox(
                widthFactor: percentage,
                child: Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 8),
        Text(
          '${(percentage * 100).toInt()}%',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
