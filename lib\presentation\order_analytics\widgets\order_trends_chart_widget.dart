import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

enum AnalyticsChartType { line, bar, pie, heatmap }

class OrderTrendsChartWidget extends StatefulWidget {
  final String title;
  final AnalyticsChartType chartType;

  const OrderTrendsChartWidget({
    super.key,
    required this.title,
    required this.chartType,
  });

  @override
  State<OrderTrendsChartWidget> createState() => _OrderTrendsChartWidgetState();
}

class _OrderTrendsChartWidgetState extends State<OrderTrendsChartWidget> {
  String selectedPeriod = 'Week';

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                _buildPeriodSelector(),
              ],
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            <PERSON><PERSON><PERSON><PERSON>(
              height: widget.chartType == AnalyticsChartType.heatmap ? 300 : 250,
              child: _buildChart(),
            ),
            if (widget.chartType == AnalyticsChartType.pie) _buildLegend(),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    final periods = widget.chartType == AnalyticsChartType.heatmap ? ['Day', 'Week'] : ['Day', 'Week', 'Month', 'Year'];

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: periods.map((period) {
          final isSelected = selectedPeriod == period;
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedPeriod = period;
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                period,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildChart() {
    switch (widget.chartType) {
      case AnalyticsChartType.line:
        return _buildLineChart();
      case AnalyticsChartType.bar:
        return _buildBarChart();
      case AnalyticsChartType.pie:
        return _buildPieChart();
      case AnalyticsChartType.heatmap:
        return _buildHeatMap();
    }
  }

  Widget _buildLineChart() {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 500,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Theme.of(context).dividerColor,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value / 1000).toStringAsFixed(0)}K',
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                if (value.toInt() < days.length) {
                  return Text(
                    days[value.toInt()],
                    style: Theme.of(context).textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: [
              const FlSpot(0, 1200),
              const FlSpot(1, 1500),
              const FlSpot(2, 1300),
              const FlSpot(3, 1800),
              const FlSpot(4, 1600),
              const FlSpot(5, 2100),
              const FlSpot(6, 1900),
            ],
            isCurved: true,
            color: Theme.of(context).colorScheme.primary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: Theme.of(context).colorScheme.primary,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: Theme.of(context).colorScheme.primary.withAlpha(26),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart() {
    return BarChart(
      BarChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 200,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Theme.of(context).dividerColor,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: Theme.of(context).textTheme.bodySmall,
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                if (value.toInt() < labels.length) {
                  return Text(
                    labels[value.toInt()],
                    style: Theme.of(context).textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [
          BarChartGroupData(
              x: 0, barRods: [BarChartRodData(toY: 850, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 1, barRods: [BarChartRodData(toY: 1200, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 2, barRods: [BarChartRodData(toY: 1100, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 3, barRods: [BarChartRodData(toY: 1350, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 4, barRods: [BarChartRodData(toY: 1250, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 5, barRods: [BarChartRodData(toY: 950, color: Theme.of(context).colorScheme.primary, width: 20)]),
          BarChartGroupData(
              x: 6, barRods: [BarChartRodData(toY: 1400, color: Theme.of(context).colorScheme.primary, width: 20)]),
        ],
      ),
    );
  }

  Widget _buildPieChart() {
    return PieChart(
      PieChartData(
        sections: [
          PieChartSectionData(
            value: 35,
            title: '35%',
            color: Theme.of(context).colorScheme.primary,
            radius: 80,
            titleStyle: GoogleFonts.inter(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          PieChartSectionData(
            value: 25,
            title: '25%',
            color: Theme.of(context).colorScheme.secondary,
            radius: 80,
            titleStyle: GoogleFonts.inter(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          PieChartSectionData(
            value: 20,
            title: '20%',
            color: Theme.of(context).colorScheme.tertiary,
            radius: 80,
            titleStyle: GoogleFonts.inter(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          PieChartSectionData(
            value: 20,
            title: '20%',
            color: Colors.grey,
            radius: 80,
            titleStyle: GoogleFonts.inter(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
        centerSpaceRadius: 60,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildHeatMap() {
    return Column(
      children: [
        Text(
          'Order Density by Hour and Day',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: 16),
        Expanded(
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 24,
              childAspectRatio: 1,
              crossAxisSpacing: 2,
              mainAxisSpacing: 2,
            ),
            itemCount: 24 * 7,
            itemBuilder: (context, index) {
              final hour = index % 24;
              final day = index ~/ 24;
              final intensity = _getHeatMapIntensity(hour, day);

              return Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(intensity),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Center(
                  child: Text(
                    hour.toString(),
                    style: GoogleFonts.inter(
                      fontSize: 8,
                      color: intensity > 0.5 ? Colors.white : Colors.black54,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  double _getHeatMapIntensity(int hour, int day) {
    // Mock data for heat map intensity
    if (hour >= 11 && hour <= 14) return 0.8; // Lunch rush
    if (hour >= 18 && hour <= 21) return 1.0; // Dinner rush
    if (hour >= 7 && hour <= 9) return 0.6; // Breakfast
    if (day >= 5) return 0.7; // Weekend
    return 0.3; // Default
  }

  Widget _buildLegend() {
    return Padding(
      padding: EdgeInsets.only(top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildLegendItem('Pizza', Theme.of(context).colorScheme.primary),
          _buildLegendItem('Burgers', Theme.of(context).colorScheme.secondary),
          _buildLegendItem('Asian', Theme.of(context).colorScheme.tertiary),
          _buildLegendItem('Others', Colors.grey),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
