import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class CategoryTabsWidget extends StatelessWidget {
  final List<String> categories;
  final TabController tabController;

  const CategoryTabsWidget({
    super.key,
    required this.categories,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.lightTheme.colorScheme.surface,
      child: Column(
        children: [
          Container(
            height: 0.1,
            color: AppTheme.lightTheme.colorScheme.outline,
          ),
          TabBar(
            controller: tabController,
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            labelColor: AppTheme.lightTheme.colorScheme.primary,
            unselectedLabelColor: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            indicatorColor: AppTheme.lightTheme.colorScheme.primary,
            indicatorWeight: 3,
            labelStyle: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w400,
            ),
            padding: EdgeInsets.symmetric(horizontal: 2),
            tabs: categories
                .map((category) => Tab(
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                        child: Text(category),
                      ),
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }
}
