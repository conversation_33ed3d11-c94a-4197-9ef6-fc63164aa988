import 'package:flutter/material.dart';

class MenuSearchWidget extends StatelessWidget {
  final String searchQuery;
  final Function(String) onSearchChanged;

  const MenuSearchWidget({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: TextFormField(
        initialValue: searchQuery,
        onChanged: onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search menu items...',
          prefixIcon: Icon(Icons.search),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () => onSearchChanged(''),
                )
              : null,
          filled: true,
          fillColor: Theme.of(context).scaffoldBackgroundColor,
        ),
      ),
    );
  }
}
