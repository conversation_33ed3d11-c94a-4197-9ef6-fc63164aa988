import 'package:flutter/material.dart';

class PosDateRangeSelectorWidget extends StatelessWidget {
  final String selectedDateRange;
  final Function(String) onDateRangeChanged;

  const PosDateRangeSelectorWidget({
    super.key,
    required this.selectedDateRange,
    required this.onDateRangeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter & Date Range',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDateRangeButtons(context),
              ),
              SizedBox(width: 16),
              _buildCustomDateButton(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeButtons(BuildContext context) {
    final options = ['Today', 'Yesterday', 'Last 7 days', 'Last 30 days', 'This Month'];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: options.map((option) {
        final isSelected = selectedDateRange == option;
        return GestureDetector(
          onTap: () => onDateRangeChanged(option),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey.shade300,
              ),
            ),
            child: Text(
              option,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCustomDateButton(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: () => _showCustomDatePicker(context),
      icon: Icon(Icons.date_range, size: 16),
      label: Text('Custom'),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size(0, 32),
      ),
    );
  }

  void _showCustomDatePicker(BuildContext context) {
    showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: DateTime.now().subtract(Duration(days: 7)),
        end: DateTime.now(),
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    ).then((dateRange) {
      if (dateRange != null) {
        final start = dateRange.start;
        final end = dateRange.end;
        final customRange = '${start.day}/${start.month} - ${end.day}/${end.month}';
        onDateRangeChanged(customRange);
      }
    });
  }
}
