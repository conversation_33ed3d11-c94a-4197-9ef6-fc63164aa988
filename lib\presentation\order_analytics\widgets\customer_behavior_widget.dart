import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class CustomerBehaviorWidget extends StatefulWidget {
  const CustomerBehaviorWidget({super.key});

  @override
  State<CustomerBehaviorWidget> createState() => _CustomerBehaviorWidgetState();
}

class _CustomerBehaviorWidgetState extends State<CustomerBehaviorWidget> {
  String selectedSegment = 'All Customers';

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Customer Behavior Analysis',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                _buildSegmentSelector(),
              ],
            ),
            SizedBox(height: 20),
            _buildOrderFrequencySection(context),
            SizedBox(height: 20),
            _buildCuisinePreferences(context),
            SizedBox(height: 20),
            _buildSeasonalTrends(context),
            SizedBox(height: 20),
            _buildPredictiveInsights(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSegmentSelector() {
    return DropdownButton<String>(
      value: selectedSegment,
      onChanged: (String? newValue) {
        setState(() {
          selectedSegment = newValue!;
        });
      },
      items: <String>['All Customers', 'New Customers', 'Regular Customers', 'VIP Customers', 'At-Risk Customers']
          .map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(value),
        );
      }).toList(),
    );
  }

  Widget _buildOrderFrequencySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Frequency Distribution',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildFrequencyCard(context, 'Daily Orders', '23%', '1,247 customers', Colors.green),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildFrequencyCard(context, 'Weekly Orders', '45%', '2,891 customers', Colors.blue),
            ),
          ],
        ),
        SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildFrequencyCard(context, 'Monthly Orders', '28%', '1,456 customers', Colors.orange),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildFrequencyCard(context, 'Occasional', '4%', '234 customers', Colors.grey),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFrequencyCard(BuildContext context, String label, String percentage, String count, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(77)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 4),
          Text(
            percentage,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
          ),
          SizedBox(height: 2),
          Text(
            count,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildCuisinePreferences(BuildContext context) {
    final cuisines = [
      {'name': 'Italian', 'percentage': 28, 'growth': '+5.2%', 'color': Colors.red},
      {'name': 'Asian', 'percentage': 24, 'growth': '+12.3%', 'color': Colors.orange},
      {'name': 'American', 'percentage': 22, 'growth': '+3.1%', 'color': Colors.blue},
      {'name': 'Mexican', 'percentage': 15, 'growth': '+8.7%', 'color': Colors.green},
      {'name': 'Mediterranean', 'percentage': 11, 'growth': '+15.4%', 'color': Colors.purple},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cuisine Preferences',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 12),
        ...cuisines.map((cuisine) => _buildCuisineItem(context, cuisine)),
      ],
    );
  }

  Widget _buildCuisineItem(BuildContext context, Map<String, dynamic> cuisine) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: cuisine['color'],
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              cuisine['name'],
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            '${cuisine['percentage']}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(width: 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(26),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              cuisine['growth'],
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeasonalTrends(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(77),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Seasonal Trends',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSeasonalCard(context, 'Spring', '15%', 'Growth in healthy options', Icons.eco),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildSeasonalCard(context, 'Summer', '22%', 'Peak ice cream orders', Icons.wb_sunny),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSeasonalCard(context, 'Fall', '18%', 'Comfort food surge', Icons.local_florist),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildSeasonalCard(context, 'Winter', '25%', 'Hot beverages spike', Icons.ac_unit),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSeasonalCard(BuildContext context, String season, String impact, String description, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
              SizedBox(width: 8),
              Text(
                season,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            impact,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
          ),
          SizedBox(height: 4),
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildPredictiveInsights(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withAlpha(13),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: Theme.of(context).colorScheme.primary),
              SizedBox(width: 8),
              Text(
                'Predictive Insights',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          SizedBox(height: 12),
          _buildPredictionItem(
            context,
            'Churn Risk',
            '156 customers at risk of churning this month',
            'Recommend targeted promotions for re-engagement',
            Colors.red,
          ),
          SizedBox(height: 8),
          _buildPredictionItem(
            context,
            'Growth Opportunity',
            'Asian cuisine trending up 12.3%',
            'Consider expanding Asian restaurant partnerships',
            Colors.green,
          ),
          SizedBox(height: 8),
          _buildPredictionItem(
            context,
            'Peak Prediction',
            'Expected 35% increase next Friday 7-9 PM',
            'Prepare additional delivery capacity',
            Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionItem(
      BuildContext context, String title, String prediction, String recommendation, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
          ),
          SizedBox(height: 4),
          Text(
            prediction,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          SizedBox(height: 4),
          Text(
            recommendation,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
          ),
        ],
      ),
    );
  }
}
