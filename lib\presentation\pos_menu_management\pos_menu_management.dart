import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/bulk_operations_widget.dart';
import './widgets/menu_category_widget.dart';
import './widgets/menu_filter_widget.dart';
import './widgets/menu_item_management_widget.dart';
import './widgets/menu_search_widget.dart';

class PosMenuManagement extends StatefulWidget {
  const PosMenuManagement({super.key});

  @override
  State<PosMenuManagement> createState() => _PosMenuManagementState();
}

class _PosMenuManagementState extends State<PosMenuManagement> {
  String selectedCategory = 'All';
  String searchQuery = '';
  Map<String, dynamic>? selectedMenuItem;
  bool isConnected = true;
  bool showBulkOperations = false;
  Set<String> selectedItems = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Text(
            'POS Menu Management',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isConnected ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isConnected ? Icons.cloud_done : Icons.cloud_off,
                  color: Colors.white,
                  size: 14,
                ),
                SizedBox(width: 4),
                Text(
                  isConnected ? 'Synced' : 'Offline',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        if (selectedItems.isNotEmpty)
          IconButton(
            icon: Icon(Icons.delete_outline),
            onPressed: _deleteSelectedItems,
          ),
        IconButton(
          icon: Icon(Icons.sync),
          onPressed: _syncMenu,
        ),
        IconButton(
          icon: Icon(Icons.settings),
          onPressed: () {},
        ),
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'bulk':
                setState(() {
                  showBulkOperations = !showBulkOperations;
                });
                break;
              case 'export':
                _exportMenu();
                break;
              case 'import':
                _importMenu();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'bulk',
              child: Row(
                children: [
                  Icon(Icons.checklist),
                  SizedBox(width: 8),
                  Text('Bulk Operations'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('Export Menu'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload),
                  SizedBox(width: 8),
                  Text('Import Menu'),
                ],
              ),
            ),
          ],
        ),
        SizedBox(width: 8),
      ],
    );
  }

  Widget _buildBody() {
    if (MediaQuery.of(context).size.width > 768) {
      return _buildTabletLayout();
    } else {
      return _buildMobileLayout();
    }
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                MenuSearchWidget(
                  searchQuery: searchQuery,
                  onSearchChanged: (query) {
                    setState(() {
                      searchQuery = query;
                    });
                  },
                ),
                MenuFilterWidget(
                  selectedCategory: selectedCategory,
                  onCategoryChanged: (category) {
                    setState(() {
                      selectedCategory = category;
                    });
                  },
                ),
                if (showBulkOperations)
                  BulkOperationsWidget(
                    selectedItems: selectedItems,
                    onBulkAction: _handleBulkAction,
                    onClose: () {
                      setState(() {
                        showBulkOperations = false;
                        selectedItems.clear();
                      });
                    },
                  ),
                Expanded(
                  child: MenuCategoryWidget(
                    category: selectedCategory,
                    searchQuery: searchQuery,
                    selectedMenuItem: selectedMenuItem,
                    showBulkOperations: showBulkOperations,
                    selectedItems: selectedItems,
                    onItemSelected: (item) {
                      setState(() {
                        selectedMenuItem = item;
                      });
                    },
                    onItemToggled: (itemId) {
                      setState(() {
                        if (selectedItems.contains(itemId)) {
                          selectedItems.remove(itemId);
                        } else {
                          selectedItems.add(itemId);
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: selectedMenuItem != null
              ? MenuItemManagementWidget(
                  menuItem: selectedMenuItem!,
                  onItemUpdated: _updateMenuItem,
                  onItemDeleted: _deleteMenuItem,
                )
              : _buildEmptyDetailView(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        MenuSearchWidget(
          searchQuery: searchQuery,
          onSearchChanged: (query) {
            setState(() {
              searchQuery = query;
            });
          },
        ),
        MenuFilterWidget(
          selectedCategory: selectedCategory,
          onCategoryChanged: (category) {
            setState(() {
              selectedCategory = category;
            });
          },
        ),
        if (showBulkOperations)
          BulkOperationsWidget(
            selectedItems: selectedItems,
            onBulkAction: _handleBulkAction,
            onClose: () {
              setState(() {
                showBulkOperations = false;
                selectedItems.clear();
              });
            },
          ),
        Expanded(
          child: MenuCategoryWidget(
            category: selectedCategory,
            searchQuery: searchQuery,
            selectedMenuItem: selectedMenuItem,
            showBulkOperations: showBulkOperations,
            selectedItems: selectedItems,
            onItemSelected: (item) {
              _showMenuItemModal(item);
            },
            onItemToggled: (itemId) {
              setState(() {
                if (selectedItems.contains(itemId)) {
                  selectedItems.remove(itemId);
                } else {
                  selectedItems.add(itemId);
                }
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyDetailView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant_menu,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 16),
          Text(
            'Select a menu item to edit',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          SizedBox(height: 8),
          Text(
            'Choose an item from the menu to manage its details, pricing, and availability',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _addNewMenuItem,
      icon: Icon(Icons.add),
      label: Text(
        'Add Item',
        style: GoogleFonts.inter(fontWeight: FontWeight.w500),
      ),
    );
  }

  void _showMenuItemModal(Map<String, dynamic> item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: MenuItemManagementWidget(
            menuItem: item,
            onItemUpdated: _updateMenuItem,
            onItemDeleted: _deleteMenuItem,
            scrollController: scrollController,
          ),
        ),
      ),
    );
  }

  void _updateMenuItem(Map<String, dynamic> updatedItem) {
    setState(() {
      selectedMenuItem = updatedItem;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Menu item updated successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteMenuItem(String itemId) {
    setState(() {
      selectedMenuItem = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Menu item deleted successfully'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _addNewMenuItem() {
    final newItem = {
      'id': 'NEW_${DateTime.now().millisecondsSinceEpoch}',
      'name': '',
      'description': '',
      'price': 0.0,
      'category': selectedCategory == 'All' ? 'Main Course' : selectedCategory,
      'isAvailable': true,
      'imageUrl': '',
      'allergens': <String>[],
      'nutritionalInfo': {},
      'preparationTime': 15,
    };

    if (MediaQuery.of(context).size.width > 768) {
      setState(() {
        selectedMenuItem = newItem;
      });
    } else {
      _showMenuItemModal(newItem);
    }
  }

  void _handleBulkAction(String action) {
    switch (action) {
      case 'enable':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${selectedItems.length} items enabled')),
        );
        break;
      case 'disable':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${selectedItems.length} items disabled')),
        );
        break;
      case 'delete':
        _deleteSelectedItems();
        break;
    }

    setState(() {
      selectedItems.clear();
    });
  }

  void _deleteSelectedItems() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${selectedItems.length} items deleted'),
        backgroundColor: Colors.red,
      ),
    );

    setState(() {
      selectedItems.clear();
    });
  }

  void _syncMenu() {
    setState(() {
      isConnected = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Menu synchronized successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportMenu() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Menu exported successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _importMenu() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Menu import started'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
