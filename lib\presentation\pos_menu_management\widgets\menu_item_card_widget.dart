import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/app_export.dart';

class MenuItemCardWidget extends StatelessWidget {
  final Map<String, dynamic> menuItem;
  final bool isSelected;
  final bool showCheckbox;
  final bool isChecked;
  final VoidCallback onTap;
  final Function(bool?) onCheckboxChanged;
  final Function(bool) onAvailabilityToggled;

  const MenuItemCardWidget({
    super.key,
    required this.menuItem,
    required this.isSelected,
    required this.showCheckbox,
    required this.isChecked,
    required this.onTap,
    required this.onCheckboxChanged,
    required this.onAvailabilityToggled,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color:
              isSelected ? Theme.of(context).colorScheme.primaryContainer.withAlpha(102) : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).dividerColor,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(context),
            _buildContent(context),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
        image: menuItem['imageUrl'].toString().isNotEmpty
            ? DecorationImage(
                image: CachedNetworkImageProvider(menuItem['imageUrl']),
                fit: BoxFit.cover,
              )
            : null,
        color: menuItem['imageUrl'].toString().isEmpty ? Theme.of(context).colorScheme.surface : null,
      ),
      child: Stack(
        children: [
          if (menuItem['imageUrl'].toString().isEmpty)
            Center(
              child: Icon(
                Icons.restaurant,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          Positioned(
            top: 8,
            left: 8,
            child: Row(
              children: [
                if (showCheckbox)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Checkbox(
                      value: isChecked,
                      onChanged: onCheckboxChanged,
                    ),
                  ),
                if (showCheckbox) SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getCategoryColor().withAlpha(179),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    menuItem['category'],
                    style: GoogleFonts.inter(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Switch(
                value: menuItem['isAvailable'],
                onChanged: onAvailabilityToggled,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
          Positioned(
            bottom: 8,
            right: 8,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '\$${menuItem['price'].toStringAsFixed(2)}',
                style: GoogleFonts.inter(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  menuItem['name'],
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (!menuItem['isAvailable'])
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'UNAVAILABLE',
                    style: GoogleFonts.inter(
                      fontSize: 8,
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 4),
          Text(
            menuItem['description'],
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8),
          if (menuItem['allergens'].isNotEmpty) ...[
            Wrap(
              spacing: 4,
              children: (menuItem['allergens'] as List).map((allergen) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    allergen,
                    style: GoogleFonts.inter(
                      fontSize: 8,
                      color: Colors.orange[800],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(12)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            size: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4),
          Text(
            '${menuItem['preparationTime']} min',
            style: GoogleFonts.inter(
              fontSize: 10,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          Spacer(),
          if (menuItem['nutritionalInfo'] != null) ...[
            Icon(
              Icons.local_fire_department,
              size: 12,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: 4),
            Text(
              '${menuItem['nutritionalInfo']['calories']} cal',
              style: GoogleFonts.inter(
                fontSize: 10,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getCategoryColor() {
    switch (menuItem['category']) {
      case 'Appetizers':
        return Colors.green;
      case 'Main Course':
        return Colors.blue;
      case 'Desserts':
        return Colors.purple;
      case 'Beverages':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
