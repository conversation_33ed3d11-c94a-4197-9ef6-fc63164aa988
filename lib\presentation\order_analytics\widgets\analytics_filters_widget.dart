import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class AnalyticsFiltersWidget extends StatefulWidget {
  const AnalyticsFiltersWidget({super.key});

  @override
  State<AnalyticsFiltersWidget> createState() => _AnalyticsFiltersWidgetState();
}

class _AnalyticsFiltersWidgetState extends State<AnalyticsFiltersWidget> {
  DateTimeRange? selectedDateRange;
  String selectedRestaurant = 'All Restaurants';
  String selectedCuisine = 'All Cuisines';
  String selectedZone = 'All Zones';
  String selectedPayment = 'All Payment Methods';
  String selectedCustomerType = 'All Customers';

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(128),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.filter_list, size: 20),
              SizedBox(width: 8),
              Text(
                'Advanced Filters',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Spacer(),
              TextButton(
                onPressed: _clearAllFilters,
                child: Text('Clear All'),
              ),
            ],
          ),
          SizedBox(height: 16),
          _buildFilterRow1(),
          SizedBox(height: 12),
          _buildFilterRow2(),
          SizedBox(height: 12),
          _buildDateRangeFilter(),
          SizedBox(height: 16),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildFilterRow1() {
    return Row(
      children: [
        Expanded(
          child: _buildDropdownFilter(
            'Restaurant',
            selectedRestaurant,
            ['All Restaurants', 'Pizza Palace', 'Burger King', 'Sushi Express', 'Taco Bell'],
            (value) => setState(() => selectedRestaurant = value!),
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: _buildDropdownFilter(
            'Cuisine Type',
            selectedCuisine,
            ['All Cuisines', 'Italian', 'Asian', 'American', 'Mexican', 'Mediterranean'],
            (value) => setState(() => selectedCuisine = value!),
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: _buildDropdownFilter(
            'Delivery Zone',
            selectedZone,
            ['All Zones', 'Downtown', 'University Area', 'Business District', 'Residential North'],
            (value) => setState(() => selectedZone = value!),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterRow2() {
    return Row(
      children: [
        Expanded(
          child: _buildDropdownFilter(
            'Payment Method',
            selectedPayment,
            ['All Payment Methods', 'Credit Card', 'Debit Card', 'Digital Wallet', 'Cash on Delivery'],
            (value) => setState(() => selectedPayment = value!),
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: _buildDropdownFilter(
            'Customer Type',
            selectedCustomerType,
            ['All Customers', 'New Customers', 'Regular Customers', 'VIP Customers', 'At-Risk Customers'],
            (value) => setState(() => selectedCustomerType = value!),
          ),
        ),
        SizedBox(width: 12),
        Expanded(child: SizedBox()), // Empty space for alignment
      ],
    );
  }

  Widget _buildDropdownFilter(
    String label,
    String value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 4),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            underline: SizedBox(),
            onChanged: onChanged,
            style: Theme.of(context).textTheme.bodyMedium,
            items: items.map<DropdownMenuItem<String>>((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: _selectDateRange,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Theme.of(context).dividerColor),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, size: 16),
                      SizedBox(width: 8),
                      Text(
                        selectedDateRange != null
                            ? '${_formatDate(selectedDateRange!.start)} - ${_formatDate(selectedDateRange!.end)}'
                            : 'Select date range',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: selectedDateRange != null
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            _buildQuickDateButton('Today'),
            SizedBox(width: 8),
            _buildQuickDateButton('This Week'),
            SizedBox(width: 8),
            _buildQuickDateButton('This Month'),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateButton(String label) {
    return OutlinedButton(
      onPressed: () => _setQuickDateRange(label),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: Size(0, 0),
      ),
      child: Text(
        label,
        style: GoogleFonts.inter(fontSize: 12),
      ),
    );
  }

  Widget _buildApplyButton() {
    return Row(
      children: [
        Spacer(),
        OutlinedButton(
          onPressed: _clearAllFilters,
          child: Text('Reset'),
        ),
        SizedBox(width: 12),
        ElevatedButton(
          onPressed: _applyFilters,
          child: Text('Apply Filters'),
        ),
      ],
    );
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: selectedDateRange,
    );
    if (picked != null && picked != selectedDateRange) {
      setState(() {
        selectedDateRange = picked;
      });
    }
  }

  void _setQuickDateRange(String period) {
    final now = DateTime.now();
    DateTimeRange range;

    switch (period) {
      case 'Today':
        range = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: now,
        );
        break;
      case 'This Week':
        final monday = now.subtract(Duration(days: now.weekday - 1));
        range = DateTimeRange(
          start: DateTime(monday.year, monday.month, monday.day),
          end: now,
        );
        break;
      case 'This Month':
        range = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: now,
        );
        break;
      default:
        return;
    }

    setState(() {
      selectedDateRange = range;
    });
  }

  void _clearAllFilters() {
    setState(() {
      selectedDateRange = null;
      selectedRestaurant = 'All Restaurants';
      selectedCuisine = 'All Cuisines';
      selectedZone = 'All Zones';
      selectedPayment = 'All Payment Methods';
      selectedCustomerType = 'All Customers';
    });
  }

  void _applyFilters() {
    // Apply filters to analytics data
    // This would typically call a callback or trigger a state update
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Filters applied successfully')),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
