import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class SocialRegistrationWidget extends StatelessWidget {
  final Function(String) onSocialLogin;

  const SocialRegistrationWidget({
    super.key,
    required this.onSocialLogin,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sign up with',
          style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
            color: AppTheme.textSecondaryLight,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2),

        // Social login buttons
        Column(
          children: [
            // Google Sign Up
            _buildSocialButton(
              icon: 'g_translate',
              label: 'Continue with Google',
              backgroundColor: Colors.white,
              textColor: AppTheme.textPrimaryLight,
              borderColor: AppTheme.dividerLight,
              onTap: () => onSocialLogin('Google'),
            ),

            SizedBox(height: 2),

            // Apple Sign Up (iOS style)
            _buildSocialButton(
              icon: 'apple',
              label: 'Continue with Apple',
              backgroundColor: AppTheme.textPrimaryLight,
              textColor: Colors.white,
              onTap: () => onSocialLogin('Apple'),
            ),

            SizedBox(height: 2),

            // Facebook Sign Up
            _buildSocialButton(
              icon: 'facebook',
              label: 'Continue with Facebook',
              backgroundColor: const Color(0xFF1877F2),
              textColor: Colors.white,
              onTap: () => onSocialLogin('Facebook'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required String icon,
    required String label,
    required Color backgroundColor,
    required Color textColor,
    Color? borderColor,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 6,
      child: OutlinedButton(
        onPressed: onTap,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          side: BorderSide(
            color: borderColor ?? backgroundColor,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: backgroundColor == Colors.white ? 1 : 0,
          shadowColor: AppTheme.shadowLight,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: icon,
              color: textColor,
              size: 5,
            ),
            SizedBox(width: 3),
            Text(
              label,
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
