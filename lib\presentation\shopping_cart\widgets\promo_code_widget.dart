import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class PromoCodeWidget extends StatefulWidget {
  final Function(String) onPromoApplied;
  final VoidCallback? onPromoRemoved;
  final String? appliedPromoCode;
  final bool isLoading;

  const PromoCodeWidget({
    super.key,
    required this.onPromoApplied,
    this.onPromoRemoved,
    this.appliedPromoCode,
    this.isLoading = false,
  });

  @override
  State<PromoCodeWidget> createState() => _PromoCodeWidgetState();
}

class _PromoCodeWidgetState extends State<PromoCodeWidget> {
  final TextEditingController _promoController = TextEditingController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _promoController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (!_isExpanded) {
        _promoController.clear();
      }
    });
  }

  void _applyPromoCode() {
    final code = _promoController.text.trim();
    if (code.isNotEmpty) {
      widget.onPromoApplied(code);
    }
  }

  void _removePromoCode() {
    if (widget.onPromoRemoved != null) {
      widget.onPromoRemoved!();
      _promoController.clear();
      setState(() {
        _isExpanded = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Column(children: [
          // Header
          InkWell(
              onTap: widget.appliedPromoCode != null ? null : _toggleExpansion,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                  padding: EdgeInsets.all(4),
                  child: Row(children: [
                    Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
                        child: CustomIconWidget(iconName: 'local_offer', size: 20)),
                    SizedBox(width: 3),
                    Expanded(
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Text(
                          widget.appliedPromoCode != null
                              ? 'Promo Applied: ${widget.appliedPromoCode}'
                              : 'Add Promo Code',
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: widget.appliedPromoCode != null
                                  ? AppTheme.lightTheme.colorScheme.primary
                                  : AppTheme.lightTheme.colorScheme.onSurface)),
                      if (widget.appliedPromoCode == null)
                        Text('Get discounts on your order',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                    ])),
                    if (widget.appliedPromoCode != null)
                      GestureDetector(
                          onTap: _removePromoCode,
                          child: Container(
                              padding: EdgeInsets.all(1),
                              decoration: BoxDecoration(
                                  color: AppTheme.lightTheme.colorScheme.error.withAlpha(51), shape: BoxShape.circle),
                              child: CustomIconWidget(
                                  iconName: 'close', color: AppTheme.lightTheme.colorScheme.error, size: 16)))
                    else
                      CustomIconWidget(
                          iconName: _isExpanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down',
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          size: 24),
                  ]))),

          // Expandable input section
          if (_isExpanded && widget.appliedPromoCode == null) ...[
            Divider(color: AppTheme.lightTheme.colorScheme.outline, height: 1),
            Padding(
                padding: EdgeInsets.all(4),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  TextField(
                      controller: _promoController,
                      decoration: InputDecoration(
                          hintText: 'Enter promo code',
                          prefixIcon: CustomIconWidget(
                              iconName: 'confirmation_number',
                              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                              size: 20),
                          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: AppTheme.lightTheme.colorScheme.outline)),
                          enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: AppTheme.lightTheme.colorScheme.outline)),
                          focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: AppTheme.lightTheme.colorScheme.primary, width: 2))),
                      textCapitalization: TextCapitalization.characters,
                      onSubmitted: (_) => _applyPromoCode()),
                  SizedBox(height: 2),
                  SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                          onPressed: widget.isLoading ? null : _applyPromoCode,
                          style: ElevatedButton.styleFrom(padding: EdgeInsets.symmetric(vertical: 2)),
                          child: widget.isLoading
                              ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                      strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                              : Text('Apply Promo Code'))),
                  SizedBox(height: 1),
                  Text('Popular codes: SAVE10, FREEDELIV, FIRST20',
                      style: AppTheme.lightTheme.textTheme.labelSmall
                          ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                ])),
          ],
        ]));
  }
}
