import 'package:flutter/material.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/customer_login/customer_login.dart';
import '../presentation/restaurant_browse/restaurant_browse.dart';
import '../presentation/customer_registration/customer_registration.dart';
import '../presentation/restaurant_menu/restaurant_menu.dart';
import '../presentation/shopping_cart/shopping_cart.dart';
import '../presentation/checkout/checkout.dart';
import '../presentation/order_tracking/order_tracking.dart';
import '../presentation/admin_dashboard/admin_dashboard.dart';
import '../presentation/restaurant_management/restaurant_management.dart';
import '../presentation/order_analytics/order_analytics.dart';
import '../presentation/pos_order_management/pos_order_management.dart';
import '../presentation/pos_menu_management/pos_menu_management.dart';
import '../presentation/pos_analytics_dashboard/pos_analytics_dashboard.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';
  static const String customerLogin = '/customer-login';
  static const String restaurantBrowse = '/restaurant-browse';
  static const String customerRegistration = '/customer-registration';
  static const String restaurantMenu = '/restaurant-menu';
  static const String shoppingCart = '/shopping-cart';
  static const String checkout = '/checkout';
  static const String orderTracking = '/order-tracking';
  static const String adminDashboard = '/admin-dashboard';
  static const String restaurantManagement = '/restaurant-management';
  static const String orderAnalytics = '/order-analytics';
  static const String posOrderManagement = '/pos-order-management';
  static const String posMenuManagement = '/pos-menu-management';
  static const String posAnalyticsDashboard = '/pos-analytics-dashboard';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    splashScreen: (context) => const SplashScreen(),
    customerLogin: (context) => const CustomerLogin(),
    restaurantBrowse: (context) => const RestaurantBrowse(),
    customerRegistration: (context) => const CustomerRegistration(),
    restaurantMenu: (context) => const RestaurantMenu(),
    shoppingCart: (context) => const ShoppingCart(),
    checkout: (context) => const Checkout(),
    orderTracking: (context) => const OrderTracking(),
    adminDashboard: (context) => const AdminDashboard(),
    restaurantManagement: (context) => const RestaurantManagement(),
    orderAnalytics: (context) => const OrderAnalytics(),
    posOrderManagement: (context) => const PosOrderManagement(),
    posMenuManagement: (context) => const PosMenuManagement(),
    posAnalyticsDashboard: (context) => const PosAnalyticsDashboard(),
    // TODO: Add your other routes here
  };
}
