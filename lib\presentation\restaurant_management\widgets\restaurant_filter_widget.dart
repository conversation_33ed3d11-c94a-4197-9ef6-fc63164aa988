import 'package:flutter/material.dart';

class RestaurantFilterWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onFiltersChanged;

  const RestaurantFilterWidget({
    super.key,
    required this.onFiltersChanged,
  });

  @override
  State<RestaurantFilterWidget> createState() => _RestaurantFilterWidgetState();
}

class _RestaurantFilterWidgetState extends State<RestaurantFilterWidget> {
  String selectedStatus = 'All';
  String selectedCuisine = 'All';
  RangeValues ratingRange = const RangeValues(0, 5);
  DateTime? fromDate;
  DateTime? toDate;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text('Clear All'),
                ),
              ],
            ),
            SizedBox(height: 16),
            _buildStatusFilter(),
            SizedBox(height: 16),
            _buildCuisineFilter(),
            SizedBox(height: 16),
            _buildRatingFilter(),
            SizedBox(height: 16),
            _buildDateFilter(),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearFilters,
                    child: Text('Reset'),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: Text('Apply'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusFilter() {
    final statuses = ['All', 'Active', 'Pending', 'Suspended', 'Inactive'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: statuses.map((status) {
            final isSelected = selectedStatus == status;
            return FilterChip(
              label: Text(status),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  selectedStatus = status;
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCuisineFilter() {
    final cuisines = ['All', 'Italian', 'Chinese', 'Mexican', 'Indian', 'American', 'Japanese'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cuisine Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: selectedCuisine,
          decoration: const InputDecoration(),
          onChanged: (value) {
            setState(() {
              selectedCuisine = value ?? 'All';
            });
          },
          items: cuisines.map((cuisine) {
            return DropdownMenuItem(
              value: cuisine,
              child: Text(cuisine),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Rating Range: ${ratingRange.start.toStringAsFixed(1)} - ${ratingRange.end.toStringAsFixed(1)}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 8),
        RangeSlider(
          values: ratingRange,
          min: 0,
          max: 5,
          divisions: 10,
          labels: RangeLabels(
            ratingRange.start.toStringAsFixed(1),
            ratingRange.end.toStringAsFixed(1),
          ),
          onChanged: (values) {
            setState(() {
              ratingRange = values;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Registration Date',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(true),
                icon: Icon(Icons.calendar_today, size: 16),
                label: Text(
                  fromDate != null ? '${fromDate!.day}/${fromDate!.month}/${fromDate!.year}' : 'From Date',
                ),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _selectDate(false),
                icon: Icon(Icons.calendar_today, size: 16),
                label: Text(
                  toDate != null ? '${toDate!.day}/${toDate!.month}/${toDate!.year}' : 'To Date',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          fromDate = picked;
        } else {
          toDate = picked;
        }
      });
    }
  }

  void _clearFilters() {
    setState(() {
      selectedStatus = 'All';
      selectedCuisine = 'All';
      ratingRange = const RangeValues(0, 5);
      fromDate = null;
      toDate = null;
    });
    _applyFilters();
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'status': selectedStatus,
      'cuisine': selectedCuisine,
      'ratingMin': ratingRange.start,
      'ratingMax': ratingRange.end,
      'fromDate': fromDate,
      'toDate': toDate,
    };
    widget.onFiltersChanged(filters);
  }
}
