import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class OrderSummaryWidget extends StatelessWidget {
  final double subtotal;
  final double deliveryFee;
  final double serviceFee;
  final double taxes;
  final double discount;
  final double total;
  final String? promoCode;
  final bool showMinimumOrderWarning;
  final double minimumOrderAmount;

  const OrderSummaryWidget({
    super.key,
    required this.subtotal,
    required this.deliveryFee,
    required this.serviceFee,
    required this.taxes,
    this.discount = 0.0,
    required this.total,
    this.promoCode,
    this.showMinimumOrderWarning = false,
    this.minimumOrderAmount = 15.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4),
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text('Order Summary', style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600)),
          SizedBox(height: 2),

          // Subtotal
          _buildSummaryRow('Subtotal', subtotal),

          // Delivery fee
          _buildSummaryRow('Delivery Fee', deliveryFee),

          // Service fee
          _buildSummaryRow('Service Fee', serviceFee),

          // Taxes
          _buildSummaryRow('Taxes & Fees', taxes),

          // Discount (if applicable)
          if (discount > 0) ...[
            _buildSummaryRow(promoCode != null ? 'Discount ($promoCode)' : 'Discount', -discount, isDiscount: true),
          ],

          SizedBox(height: 1),
          Divider(color: AppTheme.lightTheme.colorScheme.outline, thickness: 1),
          SizedBox(height: 1),

          // Total
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Text('Total', style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700)),
            Text('\${total.toStringAsFixed(2)}',
                style: AppTheme.lightTheme.textTheme.titleLarge
                    ?.copyWith(fontWeight: FontWeight.w700, color: AppTheme.lightTheme.colorScheme.primary)),
          ]),

          // Minimum order warning
          if (showMinimumOrderWarning) ...[
            SizedBox(height: 2),
            Container(
                padding: EdgeInsets.all(3),
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), border: Border.all(width: 1)),
                child: Row(children: [
                  CustomIconWidget(iconName: 'warning', size: 20),
                  SizedBox(width: 2),
                  Expanded(
                      child: Text(
                          'Minimum order of \${minimumOrderAmount.toStringAsFixed(2)} required. Add \${(minimumOrderAmount - subtotal).toStringAsFixed(2)} more.',
                          style: AppTheme.lightTheme.textTheme.bodySmall
                              ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant))),
                ])),
          ],
        ]));
  }

  Widget _buildSummaryRow(String label, double amount, {bool isDiscount = false}) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 0.5),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(label,
              style: AppTheme.lightTheme.textTheme.bodyMedium
                  ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
          Text('${isDiscount ? '-' : ''}\${amount.abs().toStringAsFixed(2)}',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isDiscount ? Colors.green : AppTheme.lightTheme.colorScheme.onSurface)),
        ]));
  }
}
