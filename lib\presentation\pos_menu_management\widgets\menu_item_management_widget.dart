import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class MenuItemManagementWidget extends StatefulWidget {
  final Map<String, dynamic> menuItem;
  final Function(Map<String, dynamic>) onItemUpdated;
  final Function(String) onItemDeleted;
  final ScrollController? scrollController;

  const MenuItemManagementWidget({
    super.key,
    required this.menuItem,
    required this.onItemUpdated,
    required this.onItemDeleted,
    this.scrollController,
  });

  @override
  State<MenuItemManagementWidget> createState() => _MenuItemManagementWidgetState();
}

class _MenuItemManagementWidgetState extends State<MenuItemManagementWidget> {
  late TextEditingController nameController;
  late TextEditingController descriptionController;
  late TextEditingController priceController;
  late TextEditingController preparationTimeController;
  late Map<String, dynamic> currentItem;
  bool hasChanges = false;

  @override
  void initState() {
    super.initState();
    currentItem = Map.from(widget.menuItem);
    nameController = TextEditingController(text: currentItem['name']);
    descriptionController = TextEditingController(text: currentItem['description']);
    priceController = TextEditingController(text: currentItem['price'].toString());
    preparationTimeController = TextEditingController(text: currentItem['preparationTime'].toString());

    // Add listeners to detect changes
    nameController.addListener(_onChanged);
    descriptionController.addListener(_onChanged);
    priceController.addListener(_onChanged);
    preparationTimeController.addListener(_onChanged);
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    priceController.dispose();
    preparationTimeController.dispose();
    super.dispose();
  }

  void _onChanged() {
    setState(() {
      hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.scrollController != null) _buildDragHandle(),
        _buildHeader(context),
        Expanded(
          child: SingleChildScrollView(
            controller: widget.scrollController,
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfo(context),
                SizedBox(height: 16),
                _buildPricingInfo(context),
                SizedBox(height: 16),
                _buildImageManagement(context),
                SizedBox(height: 16),
                _buildNutritionalInfo(context),
                SizedBox(height: 16),
                _buildAllergenInfo(context),
                SizedBox(height: 16),
                _buildAvailabilitySettings(context),
                SizedBox(height: 32),
              ],
            ),
          ),
        ),
        _buildActionButtons(context),
      ],
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentItem['name'].toString().isEmpty ? 'New Menu Item' : 'Edit Menu Item',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                if (currentItem['name'].toString().isNotEmpty)
                  Text(
                    currentItem['name'],
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                  ),
              ],
            ),
          ),
          if (hasChanges)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Unsaved',
                style: GoogleFonts.inter(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Item Name',
                prefixIcon: Icon(Icons.restaurant_menu),
              ),
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: descriptionController,
              decoration: InputDecoration(
                labelText: 'Description',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: currentItem['category'],
              decoration: InputDecoration(
                labelText: 'Category',
                prefixIcon: Icon(Icons.category),
              ),
              items: _getCategories().map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  currentItem['category'] = value;
                  hasChanges = true;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pricing & Timing',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: priceController,
                    decoration: InputDecoration(
                      labelText: 'Price (\$)',
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: preparationTimeController,
                    decoration: InputDecoration(
                      labelText: 'Prep Time (min)',
                      prefixIcon: Icon(Icons.timer),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageManagement(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Image Management',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Spacer(),
                TextButton.icon(
                  onPressed: _takePicture,
                  icon: Icon(Icons.camera_alt),
                  label: Text('Take Photo'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
                image: currentItem['imageUrl'].toString().isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(currentItem['imageUrl']),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: currentItem['imageUrl'].toString().isEmpty
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_photo_alternate,
                          size: 48,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Add Photo',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                        ),
                      ],
                    )
                  : Stack(
                      children: [
                        Positioned(
                          top: 8,
                          right: 8,
                          child: IconButton(
                            onPressed: _removeImage,
                            icon: Icon(Icons.close),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionalInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Nutritional Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              childAspectRatio: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildNutritionField('Calories', 'calories'),
                _buildNutritionField('Protein (g)', 'protein'),
                _buildNutritionField('Carbs (g)', 'carbs'),
                _buildNutritionField('Fat (g)', 'fat'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionField(String label, String key) {
    final value = currentItem['nutritionalInfo']?[key]?.toString() ?? '';
    return TextFormField(
      initialValue: value,
      decoration: InputDecoration(
        labelText: label,
        isDense: true,
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        currentItem['nutritionalInfo'] ??= {};
        currentItem['nutritionalInfo'][key] = value;
        setState(() {
          hasChanges = true;
        });
      },
    );
  }

  Widget _buildAllergenInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Allergen Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _getAllergens().map((allergen) {
                final isSelected = (currentItem['allergens'] as List).contains(allergen);
                return FilterChip(
                  label: Text(allergen),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        (currentItem['allergens'] as List).add(allergen);
                      } else {
                        (currentItem['allergens'] as List).remove(allergen);
                      }
                      hasChanges = true;
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailabilitySettings(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Availability Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            SwitchListTile(
              title: Text('Available for ordering'),
              subtitle: Text('Toggle to enable/disable this item'),
              value: currentItem['isAvailable'],
              onChanged: (value) {
                setState(() {
                  currentItem['isAvailable'] = value;
                  hasChanges = true;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (currentItem['id'].toString().isNotEmpty && !currentItem['id'].toString().startsWith('NEW_'))
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _showDeleteConfirmation(context),
                icon: Icon(Icons.delete, color: Colors.red),
                label: Text(
                  'Delete',
                  style: GoogleFonts.inter(color: Colors.red),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.red),
                ),
              ),
            ),
          if (currentItem['id'].toString().isNotEmpty && !currentItem['id'].toString().startsWith('NEW_'))
            SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: hasChanges ? _saveChanges : null,
              icon: Icon(Icons.save),
              label: Text('Save Changes'),
            ),
          ),
        ],
      ),
    );
  }

  void _takePicture() {
    // Implement camera functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Camera functionality would be implemented here')),
    );
  }

  void _removeImage() {
    setState(() {
      currentItem['imageUrl'] = '';
      hasChanges = true;
    });
  }

  void _saveChanges() {
    // Update the item with form values
    currentItem['name'] = nameController.text;
    currentItem['description'] = descriptionController.text;
    currentItem['price'] = double.tryParse(priceController.text) ?? 0.0;
    currentItem['preparationTime'] = int.tryParse(preparationTimeController.text) ?? 15;

    widget.onItemUpdated(currentItem);
    setState(() {
      hasChanges = false;
    });
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Menu Item'),
        content: Text('Are you sure you want to delete "${currentItem['name']}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onItemDeleted(currentItem['id']);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  List<String> _getCategories() {
    return [
      'Appetizers',
      'Main Course',
      'Desserts',
      'Beverages',
      'Sides',
      'Specials',
    ];
  }

  List<String> _getAllergens() {
    return [
      'Gluten',
      'Dairy',
      'Eggs',
      'Fish',
      'Shellfish',
      'Tree Nuts',
      'Peanuts',
      'Soy',
      'Sesame',
    ];
  }
}
