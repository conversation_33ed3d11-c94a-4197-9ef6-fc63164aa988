import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/category_chip_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/restaurant_card_widget.dart';

class RestaurantBrowse extends StatefulWidget {
  const RestaurantBrowse({super.key});

  @override
  State<RestaurantBrowse> createState() => _RestaurantBrowseState();
}

class _RestaurantBrowseState extends State<RestaurantBrowse> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  int _selectedBottomNavIndex = 0;
  String _selectedCategory = 'All';
  bool _isLoading = false;
  bool _isRefreshing = false;

  // Mock data for restaurants
  final List<Map<String, dynamic>> _restaurants = [
    {
      "id": 1,
      "name": "Pizza Palace",
      "cuisine": "Italian",
      "rating": 4.5,
      "deliveryTime": "25-35 min",
      "deliveryFee": "\$2.99",
      "distance": "1.2 km",
      "image": "https://images.unsplash.com/photo-1513104890138-7c749659a591?w=500&h=300&fit=crop",
      "badges": ["Free Delivery"],
      "isNew": false,
      "isFavorite": false,
      "category": "Pizza"
    },
    {
      "id": 2,
      "name": "Dragon Garden",
      "cuisine": "Chinese",
      "rating": 4.2,
      "deliveryTime": "30-40 min",
      "deliveryFee": "\$3.49",
      "distance": "2.1 km",
      "image": "https://images.unsplash.com/photo-1526318896980-cf78c088247c?w=500&h=300&fit=crop",
      "badges": ["New Restaurant"],
      "isNew": true,
      "isFavorite": true,
      "category": "Chinese"
    },
    {
      "id": 3,
      "name": "Burger Junction",
      "cuisine": "Fast Food",
      "rating": 4.0,
      "deliveryTime": "15-25 min",
      "deliveryFee": "\$1.99",
      "distance": "0.8 km",
      "image": "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=500&h=300&fit=crop",
      "badges": [],
      "isNew": false,
      "isFavorite": false,
      "category": "Fast Food"
    },
    {
      "id": 4,
      "name": "Sushi Master",
      "cuisine": "Japanese",
      "rating": 4.7,
      "deliveryTime": "35-45 min",
      "deliveryFee": "\$4.99",
      "distance": "3.2 km",
      "image": "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=500&h=300&fit=crop",
      "badges": ["Premium"],
      "isNew": false,
      "isFavorite": true,
      "category": "Japanese"
    },
    {
      "id": 5,
      "name": "Taco Fiesta",
      "cuisine": "Mexican",
      "rating": 4.3,
      "deliveryTime": "20-30 min",
      "deliveryFee": "\$2.49",
      "distance": "1.5 km",
      "image": "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=500&h=300&fit=crop",
      "badges": ["Free Delivery"],
      "isNew": false,
      "isFavorite": false,
      "category": "Mexican"
    },
    {
      "id": 6,
      "name": "Curry House",
      "cuisine": "Indian",
      "rating": 4.4,
      "deliveryTime": "25-35 min",
      "deliveryFee": "\$3.99",
      "distance": "2.8 km",
      "image": "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=500&h=300&fit=crop",
      "badges": ["Spicy"],
      "isNew": false,
      "isFavorite": false,
      "category": "Indian"
    }
  ];

  final List<String> _categories = ['All', 'Pizza', 'Chinese', 'Fast Food', 'Japanese', 'Mexican', 'Indian'];

  List<Map<String, dynamic>> get _filteredRestaurants {
    if (_selectedCategory == 'All') {
      return _restaurants;
    }
    return _restaurants.where((restaurant) => (restaurant['category'] as String) == _selectedCategory).toList();
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      _loadMoreRestaurants();
    }
  }

  Future<void> _loadMoreRestaurants() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _refreshRestaurants() async {
    setState(() {
      _isRefreshing = true;
    });

    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isRefreshing = false;
    });
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedBottomNavIndex = index;
    });

    switch (index) {
      case 0:
        // Already on Browse
        break;
      case 1:
        // Navigate to Orders
        break;
      case 2:
        // Navigate to Favorites
        break;
      case 3:
        // Navigate to Profile
        break;
    }
  }

  void _onCategorySelected(String category) {
    setState(() {
      _selectedCategory = category;
    });
  }

  void _onRestaurantTap(Map<String, dynamic> restaurant) {
    Navigator.pushNamed(context, '/restaurant-menu');
  }

  void _onRestaurantFavorite(int restaurantId) {
    setState(() {
      final index = _restaurants.indexWhere((r) => r['id'] == restaurantId);
      if (index != -1) {
        _restaurants[index]['isFavorite'] = !_restaurants[index]['isFavorite'];
      }
    });
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheetWidget(
        onApplyFilters: (filters) {
          // Apply filters logic
          Navigator.pop(context);
        },
      ),
    );
  }

  void _onSearchChanged(String query) {
    // Implement search logic
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header with search and location
            _buildHeader(),

            // Categories
            _buildCategoriesSection(),

            // Restaurant list
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshRestaurants,
                color: AppTheme.lightTheme.primaryColor,
                child: _filteredRestaurants.isEmpty
                    ? _buildEmptyState()
                    : _isRefreshing
                        ? _buildLoadingIndicator()
                        : ListView.builder(
                            controller: _scrollController,
                            padding: EdgeInsets.symmetric(horizontal: 4),
                            itemCount: _filteredRestaurants.length + (_isLoading ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index == _filteredRestaurants.length) {
                                return _buildLoadingIndicator();
                              }

                              final restaurant = _filteredRestaurants[index];
                              return RestaurantCardWidget(
                                restaurant: restaurant,
                                onTap: () => _onRestaurantTap(restaurant),
                                onFavorite: () => _onRestaurantFavorite(restaurant['id'] as int),
                              );
                            },
                          ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showFilterBottomSheet,
        backgroundColor: AppTheme.lightTheme.primaryColor,
        child: CustomIconWidget(
          iconName: 'tune',
          color: AppTheme.lightTheme.colorScheme.onPrimary,
          size: 24,
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.shadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Location indicator
          Row(
            children: [
              CustomIconWidget(
                iconName: 'location_on',
                color: AppTheme.lightTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: 2),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    // Handle location change
                  },
                  child: Text(
                    'Downtown, New York',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.lightTheme.primaryColor,
                    ),
                  ),
                ),
              ),
              CustomIconWidget(
                iconName: 'keyboard_arrow_down',
                color: AppTheme.lightTheme.primaryColor,
                size: 20,
              ),
            ],
          ),
          SizedBox(height: 3),

          // Search bar
          Container(
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.dividerColor,
              ),
            ),
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search restaurants, cuisines...',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3),
                  child: CustomIconWidget(
                    iconName: 'search',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 4,
                  vertical: 2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Container(
      height: 8,
      padding: EdgeInsets.symmetric(vertical: 1),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 4),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return CategoryChipWidget(
            category: category,
            isSelected: _selectedCategory == category,
            onTap: () => _onCategorySelected(category),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'restaurant',
            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 64,
          ),
          SizedBox(height: 2),
          Text(
            'No restaurants found',
            style: AppTheme.lightTheme.textTheme.headlineSmall,
          ),
          SizedBox(height: 1),
          Text(
            'Try adjusting your filters or location',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(4),
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.lightTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _selectedBottomNavIndex,
      onTap: _onBottomNavTap,
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppTheme.lightTheme.colorScheme.surface,
      selectedItemColor: AppTheme.lightTheme.primaryColor,
      unselectedItemColor: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
      items: [
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'explore',
            color: _selectedBottomNavIndex == 0
                ? AppTheme.lightTheme.primaryColor
                : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Browse',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'receipt_long',
            color: _selectedBottomNavIndex == 1
                ? AppTheme.lightTheme.primaryColor
                : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Orders',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'favorite',
            color: _selectedBottomNavIndex == 2
                ? AppTheme.lightTheme.primaryColor
                : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Favorites',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'person',
            color: _selectedBottomNavIndex == 3
                ? AppTheme.lightTheme.primaryColor
                : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          label: 'Profile',
        ),
      ],
    );
  }
}
