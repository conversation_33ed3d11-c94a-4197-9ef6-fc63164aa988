import 'package:flutter/material.dart';

import '../../admin_dashboard/widgets/kpi_card_widget.dart';

class PosKpiSectionWidget extends StatelessWidget {
  final String dateRange;

  const PosKpiSectionWidget({
    super.key,
    required this.dateRange,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Performance Indicators - $dateRange',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            KpiCardWidget(
              title: 'Total Revenue',
              value: '\$12,847',
              subtitle: 'Daily sales',
              icon: Icons.attach_money,
              iconColor: Colors.green,
              trend: '+18.2%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Order Count',
              value: '347',
              subtitle: 'Orders today',
              icon: Icons.shopping_cart_outlined,
              trend: '+12.5%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Average Order Value',
              value: '\$37.02',
              subtitle: 'Per order',
              icon: Icons.receipt,
              iconColor: Colors.blue,
              trend: '+4.8%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Customer Satisfaction',
              value: '4.7/5',
              subtitle: 'Average rating',
              icon: Icons.star,
              iconColor: Colors.amber,
              trend: '+0.2',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Kitchen Efficiency',
              value: '8.5 min',
              subtitle: 'Avg prep time',
              icon: Icons.timer,
              iconColor: Colors.orange,
              trend: '-1.2 min',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Peak Hour Sales',
              value: '\$2,847',
              subtitle: '12-2 PM',
              icon: Icons.trending_up,
              iconColor: Colors.purple,
              trend: '+22.1%',
              isPositive: true,
            ),
          ],
        ),
      ],
    );
  }
}
