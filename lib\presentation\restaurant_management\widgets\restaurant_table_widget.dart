import 'package:flutter/material.dart';

import './restaurant_profile_modal_widget.dart';

class RestaurantTableWidget extends StatefulWidget {
  final List<RestaurantModel> restaurants;
  final Function(RestaurantModel) onEdit;
  final Function(List<String>) onBulkAction;

  const RestaurantTableWidget({
    super.key,
    required this.restaurants,
    required this.onEdit,
    required this.onBulkAction,
  });

  @override
  State<RestaurantTableWidget> createState() => _RestaurantTableWidgetState();
}

class _RestaurantTableWidgetState extends State<RestaurantTableWidget> {
  Set<String> selectedRestaurants = {};
  bool selectAll = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildTableHeader(),
          _buildTableBody(),
          if (selectedRestaurants.isNotEmpty) _buildBulkActions(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Row(
        children: [
          Checkbox(
            value: selectAll,
            onChanged: _toggleSelectAll,
          ),
          SizedBox(width: 12),
          Expanded(flex: 3, child: Text('Restaurant', style: _headerStyle())),
          Expanded(flex: 2, child: Text('Status', style: _headerStyle())),
          Expanded(flex: 2, child: Text('Rating', style: _headerStyle())),
          Expanded(flex: 2, child: Text('Registration', style: _headerStyle())),
          Expanded(flex: 2, child: Text('Actions', style: _headerStyle())),
        ],
      ),
    );
  }

  Widget _buildTableBody() {
    return Column(
      children: widget.restaurants.map((restaurant) => _buildRestaurantRow(restaurant)).toList(),
    );
  }

  Widget _buildRestaurantRow(RestaurantModel restaurant) {
    final isSelected = selectedRestaurants.contains(restaurant.id);

    return ExpansionTile(
      leading: Checkbox(
        value: isSelected,
        onChanged: (selected) => _toggleRestaurantSelection(restaurant.id, selected ?? false),
      ),
      title: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  restaurant.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                Text(
                  restaurant.email,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: _buildStatusChip(restaurant.status),
          ),
          Expanded(
            flex: 2,
            child: Row(
              children: [
                Icon(Icons.star, color: Colors.orange, size: 16),
                SizedBox(width: 4),
                Text(restaurant.rating.toStringAsFixed(1)),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${restaurant.registrationDate.day}/${restaurant.registrationDate.month}/${restaurant.registrationDate.year}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
          Expanded(
            flex: 2,
            child: Row(
              children: [
                IconButton(
                  icon: Icon(Icons.edit, size: 16),
                  onPressed: () => _showEditModal(restaurant),
                ),
                IconButton(
                  icon: Icon(Icons.visibility, size: 16),
                  onPressed: () => _showDetailsModal(restaurant),
                ),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, size: 16),
                  onSelected: (value) => _handleAction(value, restaurant),
                  itemBuilder: (context) => [
                    PopupMenuItem(value: 'approve', child: Text('Approve')),
                    PopupMenuItem(value: 'suspend', child: Text('Suspend')),
                    PopupMenuItem(value: 'delete', child: Text('Delete')),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      children: [
        _buildExpandedContent(restaurant),
      ],
    );
  }

  Widget _buildExpandedContent(RestaurantModel restaurant) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('Phone', restaurant.phone),
              ),
              Expanded(
                child: _buildInfoItem('Commission', '${restaurant.commission}%'),
              ),
            ],
          ),
          SizedBox(height: 12),
          _buildInfoItem('Address', restaurant.address),
          SizedBox(height: 12),
          Row(
            children: [
              _buildServiceChip('Delivery', restaurant.isDeliveryEnabled),
              SizedBox(width: 8),
              _buildServiceChip('Pickup', restaurant.isPickupEnabled),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildServiceChip(String label, bool isEnabled) {
    return Chip(
      label: Text(
        label,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isEnabled ? Colors.green : Colors.grey,
            ),
      ),
      backgroundColor: isEnabled ? Colors.green.withAlpha(26) : Colors.grey.withAlpha(26),
      side: BorderSide(
        color: isEnabled ? Colors.green : Colors.grey,
        width: 1,
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status.toLowerCase()) {
      case 'active':
        color = Colors.green;
        break;
      case 'pending':
        color = Colors.orange;
        break;
      case 'suspended':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
      ),
      backgroundColor: color.withAlpha(26),
      side: BorderSide(color: color, width: 1),
    );
  }

  Widget _buildBulkActions() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(12)),
      ),
      child: Row(
        children: [
          Text(
            '${selectedRestaurants.length} selected',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
          ),
          Spacer(),
          TextButton(
            onPressed: () => _bulkAction('approve'),
            child: Text('Approve All'),
          ),
          SizedBox(width: 8),
          TextButton(
            onPressed: () => _bulkAction('suspend'),
            child: Text('Suspend All'),
          ),
          SizedBox(width: 8),
          TextButton(
            onPressed: () => _bulkAction('delete'),
            child: Text('Delete All'),
          ),
        ],
      ),
    );
  }

  TextStyle _headerStyle() {
    return Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ) ??
        const TextStyle();
  }

  void _toggleSelectAll(bool? value) {
    setState(() {
      selectAll = value ?? false;
      if (selectAll) {
        selectedRestaurants = widget.restaurants.map((r) => r.id).toSet();
      } else {
        selectedRestaurants.clear();
      }
    });
  }

  void _toggleRestaurantSelection(String id, bool selected) {
    setState(() {
      if (selected) {
        selectedRestaurants.add(id);
      } else {
        selectedRestaurants.remove(id);
      }
      selectAll = selectedRestaurants.length == widget.restaurants.length;
    });
  }

  void _showEditModal(RestaurantModel restaurant) {
    showDialog(
      context: context,
      builder: (context) => RestaurantProfileModalWidget(
        restaurant: restaurant,
        onSave: widget.onEdit,
      ),
    );
  }

  void _showDetailsModal(RestaurantModel restaurant) {
    showDialog(
      context: context,
      builder: (context) => RestaurantProfileModalWidget(
        restaurant: restaurant,
        onSave: widget.onEdit,
      ),
    );
  }

  void _handleAction(String action, RestaurantModel restaurant) {
    switch (action) {
      case 'approve':
        widget.onEdit(restaurant.copyWith(status: 'Active'));
        break;
      case 'suspend':
        widget.onEdit(restaurant.copyWith(status: 'Suspended'));
        break;
      case 'delete':
        // Handle delete action
        break;
    }
  }

  void _bulkAction(String action) {
    widget.onBulkAction(selectedRestaurants.toList());
    setState(() {
      selectedRestaurants.clear();
      selectAll = false;
    });
  }
}
