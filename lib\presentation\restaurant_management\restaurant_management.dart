import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../core/app_export.dart';
import './widgets/restaurant_filter_widget.dart';
import './widgets/restaurant_profile_modal_widget.dart';
import './widgets/restaurant_table_widget.dart';

class RestaurantManagement extends StatefulWidget {
  const RestaurantManagement({super.key});

  @override
  State<RestaurantManagement> createState() => _RestaurantManagementState();
}

class _RestaurantManagementState extends State<RestaurantManagement> {
  final TextEditingController _searchController = TextEditingController();
  List<RestaurantModel> allRestaurants = [];
  List<RestaurantModel> filteredRestaurants = [];
  Map<String, dynamic> currentFilters = {};

  @override
  void initState() {
    super.initState();
    _loadMockData();
    filteredRestaurants = allRestaurants;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadMockData() {
    allRestaurants = [
      RestaurantModel(
        id: '1',
        name: 'Pizza Palace',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Main St, Downtown',
        status: 'Active',
        rating: 4.5,
        registrationDate: DateTime.now().subtract(Duration(days: 30)),
        commission: 15.0,
        isDeliveryEnabled: true,
        isPickupEnabled: true,
      ),
      RestaurantModel(
        id: '2',
        name: 'Burger King',
        email: '<EMAIL>',
        phone: '+1234567891',
        address: '456 Oak Ave, Uptown',
        status: 'Pending',
        rating: 4.2,
        registrationDate: DateTime.now().subtract(Duration(days: 5)),
        commission: 12.0,
        isDeliveryEnabled: true,
        isPickupEnabled: false,
      ),
      RestaurantModel(
        id: '3',
        name: 'Sushi Express',
        email: '<EMAIL>',
        phone: '+1234567892',
        address: '789 Pine Rd, Midtown',
        status: 'Suspended',
        rating: 4.8,
        registrationDate: DateTime.now().subtract(Duration(days: 60)),
        commission: 18.0,
        isDeliveryEnabled: false,
        isPickupEnabled: true,
      ),
      RestaurantModel(
        id: '4',
        name: 'Taco Bell',
        email: '<EMAIL>',
        phone: '+1234567893',
        address: '321 Elm St, Eastside',
        status: 'Active',
        rating: 3.9,
        registrationDate: DateTime.now().subtract(Duration(days: 45)),
        commission: 10.0,
        isDeliveryEnabled: true,
        isPickupEnabled: true,
      ),
      RestaurantModel(
        id: '5',
        name: 'India Curry House',
        email: '<EMAIL>',
        phone: '+1234567894',
        address: '654 Maple Dr, Westside',
        status: 'Inactive',
        rating: 4.6,
        registrationDate: DateTime.now().subtract(Duration(days: 90)),
        commission: 20.0,
        isDeliveryEnabled: true,
        isPickupEnabled: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Restaurant Management',
        style: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.refresh),
          onPressed: _refreshData,
        ),
        IconButton(
          icon: Icon(Icons.download),
          onPressed: _exportData,
        ),
        IconButton(
          icon: Icon(Icons.add),
          onPressed: _addNewRestaurant,
        ),
        SizedBox(width: 8),
      ],
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSearchAndStats(),
          SizedBox(height: 16),
          _buildFilterSection(),
          SizedBox(height: 16),
          Expanded(
            child: RestaurantTableWidget(
              restaurants: filteredRestaurants,
              onEdit: _updateRestaurant,
              onBulkAction: _handleBulkAction,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndStats() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: TextField(
            controller: _searchController,
            onChanged: _performSearch,
            decoration: InputDecoration(
              hintText: 'Search restaurants...',
              prefixIcon: Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _performSearch('');
                      },
                    )
                  : null,
            ),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: _buildStatsCard(),
        ),
      ],
    );
  }

  Widget _buildStatsCard() {
    final totalRestaurants = allRestaurants.length;
    final activeRestaurants = allRestaurants.where((r) => r.status == 'Active').length;
    final pendingApprovals = allRestaurants.where((r) => r.status == 'Pending').length;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 8),
            Text('Total: $totalRestaurants'),
            Text('Active: $activeRestaurants'),
            Text('Pending: $pendingApprovals'),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return RestaurantFilterWidget(
      onFiltersChanged: _applyFilters,
    );
  }

  void _performSearch(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredRestaurants = allRestaurants;
      } else {
        filteredRestaurants = allRestaurants.where((restaurant) {
          return restaurant.name.toLowerCase().contains(query.toLowerCase()) ||
              restaurant.email.toLowerCase().contains(query.toLowerCase()) ||
              restaurant.phone.contains(query);
        }).toList();
      }
      _applyCurrentFilters();
    });
  }

  void _applyFilters(Map<String, dynamic> filters) {
    setState(() {
      currentFilters = filters;
      _applyCurrentFilters();
    });
  }

  void _applyCurrentFilters() {
    List<RestaurantModel> filtered = List.from(filteredRestaurants);

    // Apply status filter
    if (currentFilters['status'] != null && currentFilters['status'] != 'All') {
      filtered = filtered.where((r) => r.status == currentFilters['status']).toList();
    }

    // Apply rating filter
    if (currentFilters['ratingMin'] != null && currentFilters['ratingMax'] != null) {
      filtered = filtered
          .where((r) => r.rating >= currentFilters['ratingMin'] && r.rating <= currentFilters['ratingMax'])
          .toList();
    }

    // Apply date filter
    if (currentFilters['fromDate'] != null) {
      filtered = filtered.where((r) => r.registrationDate.isAfter(currentFilters['fromDate'])).toList();
    }

    if (currentFilters['toDate'] != null) {
      filtered = filtered.where((r) => r.registrationDate.isBefore(currentFilters['toDate'])).toList();
    }

    setState(() {
      filteredRestaurants = filtered;
    });
  }

  void _updateRestaurant(RestaurantModel restaurant) {
    setState(() {
      final index = allRestaurants.indexWhere((r) => r.id == restaurant.id);
      if (index != -1) {
        allRestaurants[index] = restaurant;
        _applyCurrentFilters();
      }
    });

    Fluttertoast.showToast(
      msg: "Restaurant updated successfully",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _handleBulkAction(List<String> restaurantIds) {
    setState(() {
      // Handle bulk actions here
      // For demo purposes, we'll just show a toast
    });

    Fluttertoast.showToast(
      msg: "Bulk action applied to ${restaurantIds.length} restaurants",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _refreshData() {
    setState(() {
      _loadMockData();
      filteredRestaurants = allRestaurants;
    });

    Fluttertoast.showToast(
      msg: "Data refreshed",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _exportData() {
    Fluttertoast.showToast(
      msg: "Data exported successfully",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void _addNewRestaurant() {
    final newRestaurant = RestaurantModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'New Restaurant',
      email: '<EMAIL>',
      phone: '+1234567895',
      address: 'New Address',
      status: 'Pending',
      rating: 0.0,
      registrationDate: DateTime.now(),
      commission: 15.0,
      isDeliveryEnabled: true,
      isPickupEnabled: true,
    );

    showDialog(
      context: context,
      builder: (context) => RestaurantProfileModalWidget(
        restaurant: newRestaurant,
        onSave: (restaurant) {
          setState(() {
            allRestaurants.add(restaurant);
            _applyCurrentFilters();
          });

          Fluttertoast.showToast(
            msg: "New restaurant added successfully",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        },
      ),
    );
  }
}
