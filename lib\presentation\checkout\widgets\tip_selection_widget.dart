import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/app_export.dart';

class TipSelectionWidget extends StatefulWidget {
  final double orderTotal;
  final Function(double) onTipChanged;
  final double initialTip;

  const TipSelectionWidget({
    super.key,
    required this.orderTotal,
    required this.onTipChanged,
    this.initialTip = 0.0,
  });

  @override
  State<TipSelectionWidget> createState() => _TipSelectionWidgetState();
}

class _TipSelectionWidgetState extends State<TipSelectionWidget> {
  late double selectedTip;
  String selectedTipType = 'percentage'; // 'percentage' or 'custom'
  final TextEditingController _customTipController = TextEditingController();

  final List<int> predefinedPercentages = [15, 18, 20, 25];

  @override
  void initState() {
    super.initState();
    selectedTip = widget.initialTip;

    // Determine if initial tip is a standard percentage
    if (selectedTip > 0) {
      final percentage = (selectedTip / widget.orderTotal * 100).round();
      if (predefinedPercentages.contains(percentage)) {
        selectedTipType = 'percentage';
      } else {
        selectedTipType = 'custom';
        _customTipController.text = selectedTip.toStringAsFixed(2);
      }
    }
  }

  @override
  void dispose() {
    _customTipController.dispose();
    super.dispose();
  }

  void _selectPercentageTip(int percentage) {
    final tipAmount = widget.orderTotal * (percentage / 100);
    setState(() {
      selectedTip = tipAmount;
      selectedTipType = 'percentage';
    });
    widget.onTipChanged(tipAmount);
    HapticFeedback.selectionClick();
  }

  void _selectCustomTip() {
    setState(() {
      selectedTipType = 'custom';
    });
  }

  void _updateCustomTip(String value) {
    final tipAmount = double.tryParse(value) ?? 0.0;
    setState(() {
      selectedTip = tipAmount;
    });
    widget.onTipChanged(tipAmount);
  }

  void _selectNoTip() {
    setState(() {
      selectedTip = 0.0;
      selectedTipType = 'none';
      _customTipController.clear();
    });
    widget.onTipChanged(0.0);
    HapticFeedback.selectionClick();
  }

  int? _getSelectedPercentage() {
    if (selectedTipType == 'percentage' && selectedTip > 0) {
      final percentage = (selectedTip / widget.orderTotal * 100).round();
      return predefinedPercentages.contains(percentage) ? percentage : null;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Padding(
            padding: EdgeInsets.all(4),
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(children: [
                Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: CustomIconWidget(iconName: 'volunteer_activism', size: 20)),
                SizedBox(width: 3),
                Expanded(
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Text('Add Tip',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
                  Text('Support your driver with a tip',
                      style: AppTheme.lightTheme.textTheme.bodySmall
                          ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                ])),
                if (selectedTip > 0)
                  Container(
                      padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
                      child: Text('\$${selectedTip.toStringAsFixed(2)}',
                          style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w600))),
              ]),
              SizedBox(height: 3),

              // Percentage tip buttons
              Text('Quick Tip', style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w600)),
              SizedBox(height: 1),
              Row(children: [
                ...predefinedPercentages.map((percentage) => _buildPercentageTipButton(percentage)),
                SizedBox(width: 2),
                _buildNoTipButton(),
              ]),

              SizedBox(height: 3),

              // Custom tip input
              Text('Custom Amount',
                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w600)),
              SizedBox(height: 1),
              Row(children: [
                Expanded(
                    child: TextField(
                        controller: _customTipController,
                        onChanged: _updateCustomTip,
                        onTap: _selectCustomTip,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        decoration: InputDecoration(
                            prefixText: '\$',
                            hintText: '0.00',
                            contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppTheme.lightTheme.colorScheme.outline)),
                            enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                    color: selectedTipType == 'custom'
                                        ? AppTheme.lightTheme.colorScheme.primary
                                        : AppTheme.lightTheme.colorScheme.outline)),
                            focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(color: AppTheme.lightTheme.colorScheme.primary, width: 2))))),
              ]),

              SizedBox(height: 3),

              // Driver appreciation message
              Container(
                  padding: EdgeInsets.all(3),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
                  child: Row(children: [
                    CustomIconWidget(iconName: 'favorite', size: 16),
                    SizedBox(width: 2),
                    Expanded(
                        child: Text(
                            '100% of your tip goes directly to your driver to show appreciation for their service',
                            style: AppTheme.lightTheme.textTheme.labelSmall
                                ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant))),
                  ])),
            ])));
  }

  Widget _buildPercentageTipButton(int percentage) {
    final isSelected = _getSelectedPercentage() == percentage;
    final tipAmount = widget.orderTotal * (percentage / 100);

    return Expanded(
        child: Container(
            margin: EdgeInsets.only(right: 2),
            child: InkWell(
                onTap: () => _selectPercentageTip(percentage),
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: isSelected
                                ? AppTheme.lightTheme.colorScheme.primary
                                : AppTheme.lightTheme.colorScheme.outline,
                            width: isSelected ? 2 : 1),
                        borderRadius: BorderRadius.circular(8),
                        color: isSelected ? AppTheme.lightTheme.colorScheme.primary.withAlpha(26) : Colors.transparent),
                    child: Column(children: [
                      Text('$percentage%',
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isSelected
                                  ? AppTheme.lightTheme.colorScheme.primary
                                  : AppTheme.lightTheme.colorScheme.onSurface)),
                      SizedBox(height: 0.5),
                      Text('\$${tipAmount.toStringAsFixed(2)}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                              color: isSelected
                                  ? AppTheme.lightTheme.colorScheme.primary
                                  : AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                    ])))));
  }

  Widget _buildNoTipButton() {
    final isSelected = selectedTipType == 'none';

    return Expanded(
        child: InkWell(
            onTap: _selectNoTip,
            borderRadius: BorderRadius.circular(8),
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 2),
                decoration: BoxDecoration(
                    border: Border.all(
                        color: isSelected
                            ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                            : AppTheme.lightTheme.colorScheme.outline,
                        width: isSelected ? 2 : 1),
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.onSurfaceVariant.withAlpha(26)
                        : Colors.transparent),
                child: Column(children: [
                  Text('No Tip',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                              : AppTheme.lightTheme.colorScheme.onSurface)),
                  SizedBox(height: 0.5),
                  Text('\$0.00',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: isSelected
                              ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                              : AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                ]))));
  }
}
