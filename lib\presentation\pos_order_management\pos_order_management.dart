import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/order_detail_widget.dart';
import './widgets/order_filter_widget.dart';
import './widgets/order_queue_widget.dart';

class PosOrderManagement extends StatefulWidget {
  const PosOrderManagement({super.key});

  @override
  State<PosOrderManagement> createState() => _PosOrderManagementState();
}

class _PosOrderManagementState extends State<PosOrderManagement> {
  String selectedOrderId = '';
  String currentFilter = 'All';
  bool isConnected = true;
  int notificationCount = 3;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Text(
            'POS Order Management',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isConnected ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isConnected ? Icons.cloud_done : Icons.cloud_off,
                  color: Colors.white,
                  size: 14,
                ),
                SizedBox(width: 4),
                Text(
                  isConnected ? 'Online' : 'Offline',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Stack(
            children: [
              Icon(Icons.notifications_outlined),
              if (notificationCount > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: BoxConstraints(
                      minWidth: 12,
                      minHeight: 12,
                    ),
                    child: Text(
                      '$notificationCount',
                      style: GoogleFonts.inter(
                        color: Colors.white,
                        fontSize: 8,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Icons.volume_up),
          onPressed: _toggleSoundNotifications,
        ),
        IconButton(
          icon: Icon(Icons.refresh),
          onPressed: _syncOrders,
        ),
        SizedBox(width: 8),
      ],
    );
  }

  Widget _buildBody() {
    if (MediaQuery.of(context).size.width > 768) {
      return _buildTabletLayout();
    } else {
      return _buildMobileLayout();
    }
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                OrderFilterWidget(
                  currentFilter: currentFilter,
                  onFilterChanged: (filter) {
                    setState(() {
                      currentFilter = filter;
                    });
                  },
                ),
                Expanded(
                  child: OrderQueueWidget(
                    filter: currentFilter,
                    selectedOrderId: selectedOrderId,
                    onOrderSelected: (orderId) {
                      setState(() {
                        selectedOrderId = orderId;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: selectedOrderId.isNotEmpty
              ? OrderDetailWidget(
                  orderId: selectedOrderId,
                  onStatusUpdate: _updateOrderStatus,
                )
              : _buildEmptyDetailView(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        OrderFilterWidget(
          currentFilter: currentFilter,
          onFilterChanged: (filter) {
            setState(() {
              currentFilter = filter;
            });
          },
        ),
        Expanded(
          child: OrderQueueWidget(
            filter: currentFilter,
            selectedOrderId: selectedOrderId,
            onOrderSelected: (orderId) {
              _showOrderDetailModal(orderId);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyDetailView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 16),
          Text(
            'Select an order to view details',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          SizedBox(height: 8),
          Text(
            'Choose an order from the queue to manage its status and details',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.pushNamed(context, AppRoutes.posMenuManagement);
      },
      icon: Icon(Icons.restaurant_menu),
      label: Text(
        'Menu Management',
        style: GoogleFonts.inter(fontWeight: FontWeight.w500),
      ),
    );
  }

  void _showOrderDetailModal(String orderId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: OrderDetailWidget(
            orderId: orderId,
            onStatusUpdate: _updateOrderStatus,
            scrollController: scrollController,
          ),
        ),
      ),
    );
  }

  void _updateOrderStatus(String orderId, String newStatus) {
    // Handle order status update
    setState(() {
      // Update order status in your data source
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Order $orderId status updated to $newStatus'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _toggleSoundNotifications() {
    // Toggle sound notifications
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sound notifications toggled'),
      ),
    );
  }

  void _syncOrders() {
    // Sync orders with server
    setState(() {
      isConnected = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Orders synchronized successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
