import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class PosSalesChartWidget extends StatelessWidget {
  final String title;
  final String chartType;
  final double height;

  const PosSalesChartWidget({
    super.key,
    required this.title,
    required this.chartType,
    this.height = 300,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, size: 20),
                  onSelected: (value) {
                    // Handle chart options
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'export',
                      child: Text('Export Chart'),
                    ),
                    PopupMenuItem(
                      value: 'fullscreen',
                      child: Text('Full Screen'),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16),
            SizedBox(
              height: height,
              child: _buildChart(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    switch (chartType) {
      case 'line':
        return LineChart(
          LineChartData(
            gridData: FlGridData(show: true),
            titlesData: FlTitlesData(show: true),
            borderData: FlBorderData(show: true),
            lineBarsData: [
              LineChartBarData(
                spots: [
                  FlSpot(0, 3),
                  FlSpot(1, 4),
                  FlSpot(2, 3.5),
                  FlSpot(3, 5),
                  FlSpot(4, 4.5),
                  FlSpot(5, 6),
                  FlSpot(6, 7),
                ],
                isCurved: true,
                color: Colors.blue,
                barWidth: 3,
                isStrokeCapRound: true,
                dotData: FlDotData(show: false),
                belowBarData: BarAreaData(
                  show: true,
                  color: Colors.blue.withAlpha(26),
                ),
              ),
            ],
          ),
        );
      case 'pie':
        return PieChart(
          PieChartData(
            sections: [
              PieChartSectionData(
                color: Colors.blue,
                value: 40,
                title: 'Dine-in\n40%',
                radius: 50,
              ),
              PieChartSectionData(
                color: Colors.orange,
                value: 30,
                title: 'Takeout\n30%',
                radius: 50,
              ),
              PieChartSectionData(
                color: Colors.green,
                value: 20,
                title: 'Delivery\n20%',
                radius: 50,
              ),
              PieChartSectionData(
                color: Colors.purple,
                value: 10,
                title: 'Online\n10%',
                radius: 50,
              ),
            ],
            sectionsSpace: 2,
            centerSpaceRadius: 40,
          ),
        );
      case 'bar':
        return BarChart(
          BarChartData(
            alignment: BarChartAlignment.spaceAround,
            maxY: 100,
            barTouchData: BarTouchData(enabled: false),
            titlesData: FlTitlesData(show: true),
            borderData: FlBorderData(show: false),
            barGroups: [
              BarChartGroupData(
                x: 0,
                barRods: [BarChartRodData(toY: 80, color: Colors.blue)],
              ),
              BarChartGroupData(
                x: 1,
                barRods: [BarChartRodData(toY: 60, color: Colors.orange)],
              ),
              BarChartGroupData(
                x: 2,
                barRods: [BarChartRodData(toY: 90, color: Colors.green)],
              ),
              BarChartGroupData(
                x: 3,
                barRods: [BarChartRodData(toY: 70, color: Colors.purple)],
              ),
            ],
          ),
        );
      default:
        return Center(
          child: Text('Chart not available'),
        );
    }
  }
}
