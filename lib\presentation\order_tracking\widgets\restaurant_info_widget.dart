import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class RestaurantInfoWidget extends StatelessWidget {
  final Map<String, dynamic> restaurantData;
  final VoidCallback? onCallRestaurant;
  final VoidCallback? onViewMenu;

  const RestaurantInfoWidget({
    super.key,
    required this.restaurantData,
    this.onCallRestaurant,
    this.onViewMenu,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CustomImageWidget(
                  imageUrl: restaurantData['image'] as String,
                  width: 16,
                  height: 16,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 3),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      restaurantData['name'] as String,
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.5),
                    Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'star',
                          size: 16,
                          color: Colors.amber,
                        ),
                        SizedBox(width: 1),
                        Text(
                          '${restaurantData['rating']}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 2),
                        Text(
                          '• ${restaurantData['cuisine']}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.5),
                    Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'location_on',
                          size: 16,
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 1),
                        Expanded(
                          child: Text(
                            restaurantData['address'] as String,
                            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 3),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onCallRestaurant,
                  icon: CustomIconWidget(
                    iconName: 'phone',
                    size: 20,
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                  label: Text('Call Restaurant'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2),
                  ),
                ),
              ),
              SizedBox(width: 3),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onViewMenu,
                  icon: CustomIconWidget(
                    iconName: 'restaurant_menu',
                    size: 20,
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                  label: Text('View Menu'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
