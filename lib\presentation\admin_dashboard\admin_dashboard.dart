import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/activity_feed_widget.dart';
import './widgets/dashboard_chart_widget.dart';
import './widgets/kpi_card_widget.dart';
import './widgets/quick_action_tile_widget.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Admin Dashboard',
        style: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          icon: Stack(
            children: [
              Icon(Icons.notifications_outlined),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  child: Text(
                    '3',
                    style: GoogleFonts.inter(
                      color: Colors.white,
                      fontSize: 8,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Icons.search),
          onPressed: () {},
        ),
        PopupMenuButton<String>(
          icon: CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Text(
              'A',
              style: GoogleFonts.inter(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          onSelected: (value) {},
          itemBuilder: (context) => [
            PopupMenuItem(value: 'profile', child: Text('Profile')),
            PopupMenuItem(value: 'settings', child: Text('Settings')),
            PopupMenuItem(value: 'logout', child: Text('Logout')),
          ],
        ),
        SizedBox(width: 8),
      ],
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Text(
                    'RH',
                    style: GoogleFonts.inter(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'RestaurantHub',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Admin Panel',
                  style: GoogleFonts.inter(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(Icons.dashboard, 'Dashboard', () {}),
          _buildDrawerItem(Icons.restaurant, 'Restaurant Management', () {
            Navigator.pushNamed(context, AppRoutes.restaurantManagement);
          }),
          _buildDrawerItem(Icons.menu_book, 'Menu Management', () {}),
          _buildDrawerItem(Icons.shopping_cart, 'Order Management', () {}),
          _buildDrawerItem(Icons.people, 'Customer Management', () {}),
          _buildDrawerItem(Icons.analytics, 'Analytics', () {
            Navigator.pushNamed(context, AppRoutes.orderAnalytics);
          }),
          _buildDrawerItem(Icons.support_agent, 'Support Tickets', () {}),
          _buildDrawerItem(Icons.settings, 'Settings', () {}),
          Divider(),
          _buildDrawerItem(Icons.logout, 'Logout', () {}),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon),
      title: Text(
        title,
        style: GoogleFonts.inter(),
      ),
      onTap: onTap,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildKpiSection(),
          SizedBox(height: 24),
          _buildChartsSection(),
          SizedBox(height: 24),
          _buildQuickActionsSection(),
          SizedBox(height: 24),
          ActivityFeedWidget(),
        ],
      ),
    );
  }

  Widget _buildKpiSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Performance Indicators',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            KpiCardWidget(
              title: 'Total Orders',
              value: '2,847',
              subtitle: 'Today',
              icon: Icons.shopping_cart_outlined,
              trend: '+12%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Revenue',
              value: '\$45,892',
              subtitle: 'Today',
              icon: Icons.attach_money,
              iconColor: Colors.green,
              trend: '+8%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Active Restaurants',
              value: '156',
              subtitle: 'Currently active',
              icon: Icons.restaurant,
              iconColor: Colors.blue,
              trend: '+3',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Customer Satisfaction',
              value: '4.8',
              subtitle: 'Average rating',
              icon: Icons.star_outline,
              iconColor: Colors.orange,
              trend: '+0.2',
              isPositive: true,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analytics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 16),
        DashboardChartWidget(
          title: 'Order Trends',
          chartType: ChartType.line,
        ),
        SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DashboardChartWidget(
                title: 'Daily Revenue',
                chartType: ChartType.bar,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: DashboardChartWidget(
                title: 'Order Distribution',
                chartType: ChartType.pie,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            QuickActionTileWidget(
              title: 'Approve Restaurants',
              subtitle: '5 pending applications',
              icon: Icons.approval,
              backgroundColor: Colors.green.withAlpha(26),
              onTap: () {
                Navigator.pushNamed(context, AppRoutes.restaurantManagement);
              },
            ),
            QuickActionTileWidget(
              title: 'Featured Listings',
              subtitle: 'Manage promotions',
              icon: Icons.featured_play_list,
              backgroundColor: Colors.blue.withAlpha(26),
              onTap: () {},
            ),
            QuickActionTileWidget(
              title: 'Support Tickets',
              subtitle: '12 unresolved tickets',
              icon: Icons.support_agent,
              backgroundColor: Colors.orange.withAlpha(26),
              onTap: () {},
            ),
            QuickActionTileWidget(
              title: 'Announcements',
              subtitle: 'System notifications',
              icon: Icons.campaign,
              backgroundColor: Colors.purple.withAlpha(26),
              onTap: () {},
            ),
          ],
        ),
      ],
    );
  }
}
