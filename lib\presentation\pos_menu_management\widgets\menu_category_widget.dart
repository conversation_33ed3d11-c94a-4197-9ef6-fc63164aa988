import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import './menu_item_card_widget.dart';

class MenuCategoryWidget extends StatelessWidget {
  final String category;
  final String searchQuery;
  final Map<String, dynamic>? selectedMenuItem;
  final bool showBulkOperations;
  final Set<String> selectedItems;
  final Function(Map<String, dynamic>) onItemSelected;
  final Function(String) onItemToggled;

  const MenuCategoryWidget({
    super.key,
    required this.category,
    required this.searchQuery,
    required this.selectedMenuItem,
    required this.showBulkOperations,
    required this.selectedItems,
    required this.onItemSelected,
    required this.onItemToggled,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.restaurant_menu,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                category == 'All' ? 'All Menu Items' : category,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_getFilteredItems().length} items',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _buildMenuItemsList(context),
        ),
      ],
    );
  }

  Widget _buildMenuItemsList(BuildContext context) {
    final items = _getFilteredItems();

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              'No menu items found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
            SizedBox(height: 8),
            Text(
              searchQuery.isNotEmpty
                  ? 'Try adjusting your search or filter'
                  : 'Add your first menu item to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(8),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return MenuItemCardWidget(
          menuItem: item,
          isSelected: selectedMenuItem?['id'] == item['id'],
          showCheckbox: showBulkOperations,
          isChecked: selectedItems.contains(item['id']),
          onTap: () => onItemSelected(item),
          onCheckboxChanged: (checked) {
            if (checked == true) {
              onItemToggled(item['id']);
            } else {
              onItemToggled(item['id']);
            }
          },
          onAvailabilityToggled: (available) {
            _toggleItemAvailability(item['id'], available);
          },
        );
      },
    );
  }

  List<Map<String, dynamic>> _getFilteredItems() {
    List<Map<String, dynamic>> items = _getMockMenuItems();

    // Filter by category
    if (category != 'All') {
      items = items.where((item) => item['category'] == category).toList();
    }

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      items = items.where((item) {
        final name = item['name'].toString().toLowerCase();
        final description = item['description'].toString().toLowerCase();
        final query = searchQuery.toLowerCase();
        return name.contains(query) || description.contains(query);
      }).toList();
    }

    return items;
  }

  void _toggleItemAvailability(String itemId, bool available) {
    // Handle availability toggle
  }

  List<Map<String, dynamic>> _getMockMenuItems() {
    return [
      {
        'id': 'MENU001',
        'name': 'Margherita Pizza',
        'description': 'Classic Italian pizza with fresh tomatoes, mozzarella, and basil',
        'price': 16.99,
        'category': 'Main Course',
        'isAvailable': true,
        'imageUrl': 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400',
        'allergens': ['Gluten', 'Dairy'],
        'nutritionalInfo': {
          'calories': 285,
          'protein': '12g',
          'carbs': '36g',
          'fat': '10g',
        },
        'preparationTime': 15,
      },
      {
        'id': 'MENU002',
        'name': 'Caesar Salad',
        'description': 'Crisp romaine lettuce with parmesan, croutons, and Caesar dressing',
        'price': 12.99,
        'category': 'Appetizers',
        'isAvailable': true,
        'imageUrl': 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400',
        'allergens': ['Dairy', 'Eggs'],
        'nutritionalInfo': {
          'calories': 190,
          'protein': '8g',
          'carbs': '12g',
          'fat': '14g',
        },
        'preparationTime': 8,
      },
      {
        'id': 'MENU003',
        'name': 'Grilled Salmon',
        'description': 'Fresh Atlantic salmon grilled to perfection with herbs',
        'price': 24.99,
        'category': 'Main Course',
        'isAvailable': false,
        'imageUrl': 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400',
        'allergens': ['Fish'],
        'nutritionalInfo': {
          'calories': 367,
          'protein': '39g',
          'carbs': '0g',
          'fat': '22g',
        },
        'preparationTime': 20,
      },
      {
        'id': 'MENU004',
        'name': 'Chocolate Brownie',
        'description': 'Rich chocolate brownie served warm with vanilla ice cream',
        'price': 8.99,
        'category': 'Desserts',
        'isAvailable': true,
        'imageUrl': 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400',
        'allergens': ['Gluten', 'Dairy', 'Eggs'],
        'nutritionalInfo': {
          'calories': 425,
          'protein': '6g',
          'carbs': '52g',
          'fat': '22g',
        },
        'preparationTime': 5,
      },
      {
        'id': 'MENU005',
        'name': 'Fresh Orange Juice',
        'description': 'Freshly squeezed orange juice with no added sugar',
        'price': 4.99,
        'category': 'Beverages',
        'isAvailable': true,
        'imageUrl': 'https://images.unsplash.com/photo-1600271886742-f049cd451bba?w=400',
        'allergens': [],
        'nutritionalInfo': {
          'calories': 112,
          'protein': '2g',
          'carbs': '26g',
          'fat': '0g',
        },
        'preparationTime': 3,
      },
    ];
  }
}
