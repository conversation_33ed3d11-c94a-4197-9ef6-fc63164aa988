import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class OrderTimingWidget extends StatefulWidget {
  final Function(String, DateTime?) onTimingChanged;
  final String initialTiming;
  final DateTime? initialScheduledTime;

  const OrderTimingWidget({
    super.key,
    required this.onTimingChanged,
    this.initialTiming = 'asap',
    this.initialScheduledTime,
  });

  @override
  State<OrderTimingWidget> createState() => _OrderTimingWidgetState();
}

class _OrderTimingWidgetState extends State<OrderTimingWidget> {
  late String selectedTiming;
  DateTime? scheduledTime;

  @override
  void initState() {
    super.initState();
    selectedTiming = widget.initialTiming;
    scheduledTime = widget.initialScheduledTime;
  }

  void _selectTiming(String timing) {
    setState(() {
      selectedTiming = timing;
      if (timing == 'asap') {
        scheduledTime = null;
      }
    });
    widget.onTimingChanged(timing, scheduledTime);
  }

  Future<void> _selectScheduledTime() async {
    final now = DateTime.now();
    final minimumTime = now.add(const Duration(minutes: 45)); // Minimum 45 minutes from now
    final maximumTime = now.add(const Duration(days: 7)); // Maximum 7 days from now

    final selectedDate = await showDatePicker(
        context: context,
        initialDate: scheduledTime ?? minimumTime,
        firstDate: minimumTime,
        lastDate: maximumTime,
        builder: (context, child) {
          return Theme(
              data: Theme.of(context).copyWith(
                  colorScheme:
                      Theme.of(context).colorScheme.copyWith(primary: AppTheme.lightTheme.colorScheme.primary)),
              child: child!);
        });

    if (selectedDate != null) {
      final selectedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(scheduledTime ?? minimumTime),
          builder: (context, child) {
            return Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        Theme.of(context).colorScheme.copyWith(primary: AppTheme.lightTheme.colorScheme.primary)),
                child: child!);
          });

      if (selectedTime != null) {
        final newScheduledTime =
            DateTime(selectedDate.year, selectedDate.month, selectedDate.day, selectedTime.hour, selectedTime.minute);

        // Validate minimum time
        if (newScheduledTime.isBefore(minimumTime)) {
          _showErrorMessage('Please select a time at least 45 minutes from now');
          return;
        }

        setState(() {
          scheduledTime = newScheduledTime;
          selectedTiming = 'scheduled';
        });
        widget.onTimingChanged(selectedTiming, scheduledTime);
      }
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.lightTheme.colorScheme.error,
        behavior: SnackBarBehavior.floating));
  }

  String _formatScheduledTime(DateTime time) {
    final now = DateTime.now();
    final isToday = time.day == now.day && time.month == now.month && time.year == now.year;
    final isTomorrow =
        time.day == now.add(const Duration(days: 1)).day && time.month == now.month && time.year == now.year;

    String dateStr;
    if (isToday) {
      dateStr = 'Today';
    } else if (isTomorrow) {
      dateStr = 'Tomorrow';
    } else {
      dateStr = '${time.day}/${time.month}/${time.year}';
    }

    final timeStr = TimeOfDay.fromDateTime(time).format(context);
    return '$dateStr at $timeStr';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: Padding(
            padding: EdgeInsets.all(4),
            child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(children: [
                Container(
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                        borderRadius: BorderRadius.circular(8)),
                    child: CustomIconWidget(
                        iconName: 'schedule', color: AppTheme.lightTheme.colorScheme.primary, size: 20)),
                SizedBox(width: 3),
                Text('Order Timing',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
              ]),
              SizedBox(height: 2),

              // ASAP option
              _buildTimingOption(
                  value: 'asap',
                  title: 'ASAP (25-35 min)',
                  subtitle: 'Delivered as soon as possible',
                  icon: 'flash_on',
                  iconColor: AppTheme.lightTheme.colorScheme.primary),

              SizedBox(height: 1),

              // Scheduled option
              _buildTimingOption(
                  value: 'scheduled',
                  title: 'Schedule for later',
                  subtitle: scheduledTime != null
                      ? _formatScheduledTime(scheduledTime!)
                      : 'Choose your preferred delivery time',
                  icon: 'event',
                  iconColor: AppTheme.lightTheme.colorScheme.primary,
                  onTap: _selectScheduledTime),

              if (selectedTiming == 'scheduled' && scheduledTime == null) ...[
                SizedBox(height: 2),
                Container(
                    padding: EdgeInsets.all(3),
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), border: Border.all(width: 1)),
                    child: Row(children: [
                      CustomIconWidget(iconName: 'info', size: 20),
                      SizedBox(width: 2),
                      Expanded(
                          child: Text('Please select a date and time for your scheduled delivery',
                              style: AppTheme.lightTheme.textTheme.bodySmall
                                  ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant))),
                    ])),
              ],

              SizedBox(height: 2),

              // Information note
              Container(
                  padding: EdgeInsets.all(3),
                  decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.primary.withAlpha(26),
                      borderRadius: BorderRadius.circular(8)),
                  child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    CustomIconWidget(
                        iconName: 'info_outline', color: AppTheme.lightTheme.colorScheme.primary, size: 16),
                    SizedBox(width: 2),
                    Expanded(
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Text('Delivery Time Notice',
                          style: AppTheme.lightTheme.textTheme.labelMedium
                              ?.copyWith(color: AppTheme.lightTheme.colorScheme.primary, fontWeight: FontWeight.w600)),
                      SizedBox(height: 0.5),
                      Text(
                          'Scheduled orders must be placed at least 45 minutes in advance. Actual delivery time may vary based on restaurant preparation time and driver availability.',
                          style: AppTheme.lightTheme.textTheme.labelSmall
                              ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                    ])),
                  ])),
            ])));
  }

  Widget _buildTimingOption({
    required String value,
    required String title,
    required String subtitle,
    required String icon,
    required Color iconColor,
    VoidCallback? onTap,
  }) {
    final isSelected = selectedTiming == value;

    return Container(
        decoration: BoxDecoration(
            border: Border.all(
                color: isSelected ? AppTheme.lightTheme.colorScheme.primary : AppTheme.lightTheme.colorScheme.outline,
                width: isSelected ? 2 : 1),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? AppTheme.lightTheme.colorScheme.primary.withAlpha(26) : Colors.transparent),
        child: InkWell(
            onTap: onTap ?? (() => _selectTiming(value)),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
                padding: EdgeInsets.all(3),
                child: Row(children: [
                  Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(color: iconColor.withAlpha(51), borderRadius: BorderRadius.circular(8)),
                      child: CustomIconWidget(iconName: icon, color: iconColor, size: 20)),
                  SizedBox(width: 3),
                  Expanded(
                      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Text(title, style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600)),
                    SizedBox(height: 0.5),
                    Text(subtitle,
                        style: AppTheme.lightTheme.textTheme.bodySmall
                            ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                  ])),
                  Radio<String>(
                      value: value,
                      groupValue: selectedTiming,
                      onChanged: (newValue) {
                        if (newValue != null) {
                          _selectTiming(newValue);
                        }
                      },
                      activeColor: AppTheme.lightTheme.colorScheme.primary),
                ]))));
  }
}
