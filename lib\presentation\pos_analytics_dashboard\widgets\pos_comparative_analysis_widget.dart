import 'package:flutter/material.dart';

class PosComparativeAnalysisWidget extends StatelessWidget {
  const PosComparativeAnalysisWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Comparative Analysis',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            _buildComparisonSection(
              context,
              'Week-over-Week',
              [
                ComparisonData('Sales', '\$12,847', '\$10,234', '+25.5%', true),
                ComparisonData('Orders', '347', '298', '+16.4%', true),
                ComparisonData('Avg Order Value', '\$37.02', '\$34.34', '****%', true),
              ],
            ),
            Sized<PERSON><PERSON>(height: 16),
            _buildComparisonSection(
              context,
              'Month-over-Month',
              [
                ComparisonData('Revenue', '\$387,420', '\$365,890', '+5.9%', true),
                ComparisonData('Customer Count', '2,847', '2,634', '+8.1%', true),
                ComparisonData('Return Rate', '68%', '65%', '+4.6%', true),
              ],
            ),
            SizedBox(height: 16),
            _buildTrendIdentification(),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonSection(BuildContext context, String title, List<ComparisonData> data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: data.map((item) => _buildComparisonRow(item)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonRow(ComparisonData data) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              data.metric,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              data.current,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              data.previous,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: data.isPositive ? Colors.green.withAlpha(26) : Colors.red.withAlpha(26),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              data.change,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: data.isPositive ? Colors.green : Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendIdentification() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Automated Trend Identification',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8),
        _buildTrendItem(
          'Positive Trend',
          'Weekend sales showing consistent growth',
          Icons.trending_up,
          Colors.green,
        ),
        _buildTrendItem(
          'Opportunity',
          'Breakfast orders below industry average',
          Icons.info_outline,
          Colors.orange,
        ),
        _buildTrendItem(
          'Alert',
          'Delivery orders decreased 5% this week',
          Icons.warning_outlined,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildTrendItem(String type, String description, IconData icon, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: color),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ComparisonData {
  final String metric;
  final String current;
  final String previous;
  final String change;
  final bool isPositive;

  ComparisonData(this.metric, this.current, this.previous, this.change, this.isPositive);
}
