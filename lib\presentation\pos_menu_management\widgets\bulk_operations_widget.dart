import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class BulkOperationsWidget extends StatelessWidget {
  final Set<String> selectedItems;
  final Function(String) onBulkAction;
  final VoidCallback onClose;

  const BulkOperationsWidget({
    super.key,
    required this.selectedItems,
    required this.onBulkAction,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Bulk Operations',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              Spacer(),
              TextButton(
                onPressed: onClose,
                child: Text(
                  'Cancel',
                  style: GoogleFonts.inter(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            '${selectedItems.length} items selected',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          SizedBox(height: 12),
          Wrap(
            spacing: 8,
            children: [
              _buildActionChip(
                context,
                'Enable All',
                Icons.check_circle,
                Colors.green,
                () => onBulkAction('enable'),
              ),
              _buildActionChip(
                context,
                'Disable All',
                Icons.cancel,
                Colors.orange,
                () => onBulkAction('disable'),
              ),
              _buildActionChip(
                context,
                'Delete All',
                Icons.delete,
                Colors.red,
                () => _showDeleteConfirmation(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionChip(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ActionChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      onPressed: selectedItems.isNotEmpty ? onPressed : null,
      backgroundColor: color.withAlpha(26),
      side: BorderSide(color: color.withAlpha(102)),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Selected Items'),
        content: Text(
          'Are you sure you want to delete ${selectedItems.length} selected items? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onBulkAction('delete');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }
}
