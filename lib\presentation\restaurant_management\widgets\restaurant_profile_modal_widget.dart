import 'package:flutter/material.dart';

class RestaurantProfileModalWidget extends StatefulWidget {
  final RestaurantModel restaurant;
  final Function(RestaurantModel) onSave;

  const RestaurantProfileModalWidget({
    super.key,
    required this.restaurant,
    required this.onSave,
  });

  @override
  State<RestaurantProfileModalWidget> createState() => _RestaurantProfileModalWidgetState();
}

class _RestaurantProfileModalWidgetState extends State<RestaurantProfileModalWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _commissionController;

  String selectedStatus = 'Active';
  bool isDeliveryEnabled = true;
  bool isPickupEnabled = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.restaurant.name);
    _emailController = TextEditingController(text: widget.restaurant.email);
    _phoneController = TextEditingController(text: widget.restaurant.phone);
    _addressController = TextEditingController(text: widget.restaurant.address);
    _commissionController = TextEditingController(text: widget.restaurant.commission.toString());
    selectedStatus = widget.restaurant.status;
    isDeliveryEnabled = widget.restaurant.isDeliveryEnabled;
    isPickupEnabled = widget.restaurant.isPickupEnabled;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _commissionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 80,
        height: 90,
        padding: EdgeInsets.all(24),
        child: Column(
          children: [
            _buildHeader(),
            SizedBox(height: 16),
            _buildTabBar(),
            SizedBox(height: 16),
            Expanded(
              child: _buildTabBarView(),
            ),
            SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Restaurant Profile',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: [
        Tab(text: 'Basic Info'),
        Tab(text: 'Settings'),
        Tab(text: 'Documents'),
        Tab(text: 'Analytics'),
      ],
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildBasicInfoTab(),
        _buildSettingsTab(),
        _buildDocumentsTab(),
        _buildAnalyticsTab(),
      ],
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField('Restaurant Name', _nameController),
          SizedBox(height: 16),
          _buildTextField('Email', _emailController),
          SizedBox(height: 16),
          _buildTextField('Phone', _phoneController),
          SizedBox(height: 16),
          _buildTextField('Address', _addressController, maxLines: 3),
          SizedBox(height: 16),
          _buildStatusDropdown(),
          SizedBox(height: 16),
          _buildTextField('Commission Rate (%)', _commissionController),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          SwitchListTile(
            title: Text('Delivery Service'),
            subtitle: Text('Enable delivery orders'),
            value: isDeliveryEnabled,
            onChanged: (value) {
              setState(() {
                isDeliveryEnabled = value;
              });
            },
          ),
          SwitchListTile(
            title: Text('Pickup Service'),
            subtitle: Text('Enable pickup orders'),
            value: isPickupEnabled,
            onChanged: (value) {
              setState(() {
                isPickupEnabled = value;
              });
            },
          ),
          SizedBox(height: 20),
          _buildOperatingHours(),
        ],
      ),
    );
  }

  Widget _buildDocumentsTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildDocumentItem('Business License', 'Verified', Icons.check_circle, Colors.green),
          SizedBox(height: 12),
          _buildDocumentItem('Food Safety Certificate', 'Pending', Icons.pending, Colors.orange),
          SizedBox(height: 12),
          _buildDocumentItem('Insurance Document', 'Verified', Icons.check_circle, Colors.green),
          SizedBox(height: 12),
          _buildDocumentItem('Tax Registration', 'Rejected', Icons.cancel, Colors.red),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildMetricCard('Total Orders', '1,247', Icons.shopping_cart),
          SizedBox(height: 12),
          _buildMetricCard('Monthly Revenue', '\$12,456', Icons.attach_money),
          SizedBox(height: 12),
          _buildMetricCard('Average Rating', '4.6', Icons.star),
          SizedBox(height: 12),
          _buildMetricCard('Average Delivery Time', '28 min', Icons.timer),
        ],
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController controller, {int maxLines = 1}) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    final statuses = ['Active', 'Pending', 'Suspended', 'Inactive'];

    return DropdownButtonFormField<String>(
      value: selectedStatus,
      decoration: InputDecoration(
        labelText: 'Status',
        border: OutlineInputBorder(),
      ),
      onChanged: (value) {
        setState(() {
          selectedStatus = value ?? 'Active';
        });
      },
      items: statuses.map((status) {
        return DropdownMenuItem(
          value: status,
          child: Text(status),
        );
      }).toList(),
    );
  }

  Widget _buildOperatingHours() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Operating Hours',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 12),
            ...['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                .map((day) => _buildDayHours(day)),
          ],
        ),
      ),
    );
  }

  Widget _buildDayHours(String day) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(day),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      hintText: '09:00',
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Text('to'),
                ),
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      hintText: '22:00',
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(String title, String status, IconData icon, Color statusColor) {
    return Card(
      child: ListTile(
        leading: Icon(Icons.description),
        title: Text(title),
        subtitle: Text('Last updated: 2 days ago'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: statusColor, size: 16),
            SizedBox(width: 4),
            Text(
              status,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon) {
    return Card(
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(title),
        trailing: Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveChanges,
            child: Text('Save Changes'),
          ),
        ),
      ],
    );
  }

  void _saveChanges() {
    final updatedRestaurant = widget.restaurant.copyWith(
      name: _nameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
      address: _addressController.text,
      status: selectedStatus,
      commission: double.tryParse(_commissionController.text) ?? 0.0,
      isDeliveryEnabled: isDeliveryEnabled,
      isPickupEnabled: isPickupEnabled,
    );

    widget.onSave(updatedRestaurant);
    Navigator.of(context).pop();
  }
}

class RestaurantModel {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String status;
  final double rating;
  final DateTime registrationDate;
  final double commission;
  final bool isDeliveryEnabled;
  final bool isPickupEnabled;

  RestaurantModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.status,
    required this.rating,
    required this.registrationDate,
    required this.commission,
    required this.isDeliveryEnabled,
    required this.isPickupEnabled,
  });

  RestaurantModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? status,
    double? rating,
    DateTime? registrationDate,
    double? commission,
    bool? isDeliveryEnabled,
    bool? isPickupEnabled,
  }) {
    return RestaurantModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      registrationDate: registrationDate ?? this.registrationDate,
      commission: commission ?? this.commission,
      isDeliveryEnabled: isDeliveryEnabled ?? this.isDeliveryEnabled,
      isPickupEnabled: isPickupEnabled ?? this.isPickupEnabled,
    );
  }
}
