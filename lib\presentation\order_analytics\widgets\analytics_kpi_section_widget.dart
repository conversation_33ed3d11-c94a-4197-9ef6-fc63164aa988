import 'package:flutter/material.dart';

import '../../admin_dashboard/widgets/kpi_card_widget.dart';

class AnalyticsKpiSectionWidget extends StatelessWidget {
  const AnalyticsKpiSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Key Performance Indicators',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          childAspectRatio: 1.3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            KpiCardWidget(
              title: 'Total Orders',
              value: '8,547',
              subtitle: 'Last 7 days',
              icon: Icons.shopping_cart_outlined,
              trend: '+15.3%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Average Order Value',
              value: '\$32.85',
              subtitle: 'Per order',
              icon: Icons.attach_money,
              iconColor: Colors.green,
              trend: '+8.7%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Completion Rate',
              value: '94.2%',
              subtitle: 'Successfully delivered',
              icon: Icons.check_circle_outline,
              iconColor: Colors.blue,
              trend: '+2.1%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Customer Retention',
              value: '76.8%',
              subtitle: 'Repeat customers',
              icon: Icons.people_outline,
              iconColor: Colors.orange,
              trend: '+5.4%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Peak Hour Orders',
              value: '1,247',
              subtitle: '6-8 PM today',
              icon: Icons.schedule,
              iconColor: Colors.purple,
              trend: '+12.9%',
              isPositive: true,
            ),
            KpiCardWidget(
              title: 'Revenue Today',
              value: '\$28,463',
              subtitle: 'Total revenue',
              icon: Icons.trending_up,
              iconColor: Colors.teal,
              trend: '+18.2%',
              isPositive: true,
            ),
          ],
        ),
      ],
    );
  }
}
