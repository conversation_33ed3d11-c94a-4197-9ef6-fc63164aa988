import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class MenuFilterWidget extends StatelessWidget {
  final String selectedCategory;
  final Function(String) onCategoryChanged;

  const MenuFilterWidget({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter by Category',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _getCategories().map((category) {
                final isSelected = selectedCategory == category;
                return GestureDetector(
                  onTap: () => onCategoryChanged(category),
                  child: Container(
                    margin: EdgeInsets.only(right: 8),
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).dividerColor,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getCategoryIcon(category),
                          size: 16,
                          color: isSelected ? Colors.white : Theme.of(context).colorScheme.onSurface,
                        ),
                        SizedBox(width: 4),
                        Text(
                          category,
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: isSelected ? Colors.white : Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getCategories() {
    return [
      'All',
      'Appetizers',
      'Main Course',
      'Desserts',
      'Beverages',
      'Sides',
      'Specials',
    ];
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'All':
        return Icons.all_inclusive;
      case 'Appetizers':
        return Icons.restaurant;
      case 'Main Course':
        return Icons.dinner_dining;
      case 'Desserts':
        return Icons.cake;
      case 'Beverages':
        return Icons.local_drink;
      case 'Sides':
        return Icons.rice_bowl;
      case 'Specials':
        return Icons.star;
      default:
        return Icons.category;
    }
  }
}
