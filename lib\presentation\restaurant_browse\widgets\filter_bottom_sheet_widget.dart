import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class FilterBottomSheetWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onApplyFilters;

  const FilterBottomSheetWidget({
    super.key,
    required this.onApplyFilters,
  });

  @override
  State<FilterBottomSheetWidget> createState() => _FilterBottomSheetWidgetState();
}

class _FilterBottomSheetWidgetState extends State<FilterBottomSheetWidget> {
  RangeValues _deliveryTimeRange = const RangeValues(15, 60);
  double _minRating = 3.0;
  RangeValues _priceRange = const RangeValues(0, 50);
  final Set<String> _selectedDietaryPreferences = {};

  final List<String> _dietaryOptions = [
    'Vegetarian',
    'Vegan',
    'Gluten-Free',
    'Halal',
    'Kosher',
    'Keto',
    'Low-Carb',
    'Dairy-Free'
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 12,
            height: 0.5,
            margin: EdgeInsets.symmetric(vertical: 2),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4),
            child: Row(
              children: [
                Text(
                  'Filters',
                  style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: Text(
                    'Reset',
                    style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                      color: AppTheme.lightTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          Divider(color: AppTheme.lightTheme.dividerColor),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 2),

                  // Delivery Time
                  _buildDeliveryTimeSection(),

                  SizedBox(height: 4),

                  // Rating
                  _buildRatingSection(),

                  SizedBox(height: 4),

                  // Price Range
                  _buildPriceRangeSection(),

                  SizedBox(height: 4),

                  // Dietary Preferences
                  _buildDietaryPreferencesSection(),

                  SizedBox(height: 4),
                ],
              ),
            ),
          ),

          // Apply button
          Container(
            padding: EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              border: Border(
                top: BorderSide(color: AppTheme.lightTheme.dividerColor),
              ),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 2),
                ),
                child: Text(
                  'Apply Filters',
                  style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Time',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2),
        Text(
          '${_deliveryTimeRange.start.round()} - ${_deliveryTimeRange.end.round()} minutes',
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            color: AppTheme.lightTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        RangeSlider(
          values: _deliveryTimeRange,
          min: 10,
          max: 90,
          divisions: 16,
          activeColor: AppTheme.lightTheme.primaryColor,
          inactiveColor: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3),
          onChanged: (values) {
            setState(() {
              _deliveryTimeRange = values;
            });
          },
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Minimum Rating',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2),
        Row(
          children: [
            CustomIconWidget(
              iconName: 'star',
              color: AppTheme.lightTheme.colorScheme.tertiary,
              size: 20,
            ),
            SizedBox(width: 2),
            Text(
              _minRating.toStringAsFixed(1),
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Slider(
          value: _minRating,
          min: 1.0,
          max: 5.0,
          divisions: 8,
          activeColor: AppTheme.lightTheme.primaryColor,
          inactiveColor: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3),
          onChanged: (value) {
            setState(() {
              _minRating = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildPriceRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Price Range',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2),
        Text(
          '\$${_priceRange.start.round()} - \$${_priceRange.end.round()}',
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            color: AppTheme.lightTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        RangeSlider(
          values: _priceRange,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: AppTheme.lightTheme.primaryColor,
          inactiveColor: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3),
          onChanged: (values) {
            setState(() {
              _priceRange = values;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDietaryPreferencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dietary Preferences',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2),
        Wrap(
          spacing: 2,
          runSpacing: 1,
          children: _dietaryOptions.map((option) {
            final isSelected = _selectedDietaryPreferences.contains(option);
            return GestureDetector(
              onTap: () {
                setState(() {
                  isSelected ? _selectedDietaryPreferences.remove(option) : _selectedDietaryPreferences.add(option);
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.lightTheme.primaryColor : AppTheme.lightTheme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppTheme.lightTheme.primaryColor : AppTheme.lightTheme.dividerColor,
                  ),
                ),
                child: Text(
                  option,
                  style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                    color: isSelected
                        ? AppTheme.lightTheme.colorScheme.onPrimary
                        : AppTheme.lightTheme.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _resetFilters() {
    setState(() {
      _deliveryTimeRange = const RangeValues(15, 60);
      _minRating = 3.0;
      _priceRange = const RangeValues(0, 50);
      _selectedDietaryPreferences.clear();
    });
  }

  void _applyFilters() {
    final filters = {
      'deliveryTimeRange': _deliveryTimeRange,
      'minRating': _minRating,
      'priceRange': _priceRange,
      'dietaryPreferences': _selectedDietaryPreferences.toList(),
    };

    widget.onApplyFilters(filters);
  }
}
