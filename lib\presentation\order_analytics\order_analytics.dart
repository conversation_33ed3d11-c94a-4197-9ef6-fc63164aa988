import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './widgets/analytics_filters_widget.dart';
import './widgets/analytics_kpi_section_widget.dart';
import './widgets/customer_behavior_widget.dart';
import './widgets/export_controls_widget.dart';
import './widgets/geographic_distribution_widget.dart';
import './widgets/order_trends_chart_widget.dart';
import './widgets/real_time_monitoring_widget.dart';
import './widgets/revenue_analytics_widget.dart';

class OrderAnalytics extends StatefulWidget {
  const OrderAnalytics({super.key});

  @override
  State<OrderAnalytics> createState() => _OrderAnalyticsState();
}

class _OrderAnalyticsState extends State<OrderAnalytics> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String selectedDateRange = 'Last 7 days';
  bool showFilters = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Order Analytics',
        style: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize: 20,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            showFilters ? Icons.filter_list : Icons.filter_list_outlined,
            color: showFilters ? Theme.of(context).colorScheme.primary : null,
          ),
          onPressed: () {
            setState(() {
              showFilters = !showFilters;
            });
          },
          tooltip: 'Toggle Filters',
        ),
        IconButton(
          icon: Icon(Icons.refresh),
          onPressed: () {
            // Refresh analytics data
          },
          tooltip: 'Refresh Data',
        ),
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'export':
                _showExportDialog();
                break;
              case 'settings':
                // Navigate to analytics settings
                break;
              case 'help':
                _showHelpDialog();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 18),
                  SizedBox(width: 8),
                  Text('Export Report'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, size: 18),
                  SizedBox(width: 8),
                  Text('Settings'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, size: 18),
                  SizedBox(width: 8),
                  Text('Help'),
                ],
              ),
            ),
          ],
        ),
        SizedBox(width: 8),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: [
          Tab(
            icon: Icon(Icons.dashboard, size: 20),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.trending_up, size: 20),
            text: 'Trends',
          ),
          Tab(
            icon: Icon(Icons.map, size: 20),
            text: 'Geography',
          ),
          Tab(
            icon: Icon(Icons.people, size: 20),
            text: 'Customers',
          ),
        ],
      ),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Text(
                    'RH',
                    style: GoogleFonts.inter(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'RestaurantHub',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Analytics Dashboard',
                  style: GoogleFonts.inter(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(Icons.dashboard, 'Dashboard', () {
            Navigator.pushNamed(context, AppRoutes.adminDashboard);
          }),
          _buildDrawerItem(Icons.restaurant, 'Restaurant Management', () {
            Navigator.pushNamed(context, AppRoutes.restaurantManagement);
          }),
          _buildDrawerItem(Icons.analytics, 'Order Analytics', () {}, isSelected: true),
          _buildDrawerItem(Icons.menu_book, 'Menu Management', () {}),
          _buildDrawerItem(Icons.shopping_cart, 'Order Management', () {}),
          _buildDrawerItem(Icons.people, 'Customer Management', () {}),
          _buildDrawerItem(Icons.support_agent, 'Support Tickets', () {}),
          _buildDrawerItem(Icons.settings, 'Settings', () {}),
          Divider(),
          _buildDrawerItem(Icons.logout, 'Logout', () {}),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap, {bool isSelected = false}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected ? Theme.of(context).colorScheme.primary.withAlpha(26) : null,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
        ),
        title: Text(
          title,
          style: GoogleFonts.inter(
            color: isSelected ? Theme.of(context).colorScheme.primary : null,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        if (showFilters) AnalyticsFiltersWidget(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildTrendsTab(),
              _buildGeographyTab(),
              _buildCustomersTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AnalyticsKpiSectionWidget(),
          SizedBox(height: 24),
          RealTimeMonitoringWidget(),
          SizedBox(height: 24),
          OrderTrendsChartWidget(
            title: 'Order Volume Trends',
            chartType: AnalyticsChartType.line,
          ),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: RevenueAnalyticsWidget(),
              ),
              SizedBox(width: 16),
              Expanded(
                child: OrderTrendsChartWidget(
                  title: 'Order Distribution',
                  chartType: AnalyticsChartType.pie,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrendsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          OrderTrendsChartWidget(
            title: 'Peak Hours Analysis',
            chartType: AnalyticsChartType.heatmap,
          ),
          SizedBox(height: 24),
          OrderTrendsChartWidget(
            title: 'Weekly Order Patterns',
            chartType: AnalyticsChartType.bar,
          ),
          SizedBox(height: 24),
          OrderTrendsChartWidget(
            title: 'Monthly Revenue Trends',
            chartType: AnalyticsChartType.line,
          ),
        ],
      ),
    );
  }

  Widget _buildGeographyTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          GeographicDistributionWidget(),
          SizedBox(height: 24),
          OrderTrendsChartWidget(
            title: 'Delivery Zone Performance',
            chartType: AnalyticsChartType.bar,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          CustomerBehaviorWidget(),
          SizedBox(height: 24),
          OrderTrendsChartWidget(
            title: 'Customer Retention',
            chartType: AnalyticsChartType.line,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        _showExportDialog();
      },
      icon: Icon(Icons.download),
      label: Text('Export'),
      backgroundColor: Theme.of(context).colorScheme.primary,
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: EdgeInsets.all(24),
          child: ExportControlsWidget(),
        ),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Analytics Help'),
        content: Text(
          'This dashboard provides comprehensive order analytics including:\n\n'
          '• Real-time order monitoring\n'
          '• Revenue and performance metrics\n'
          '• Geographic distribution analysis\n'
          '• Customer behavior insights\n'
          '• Trend analysis and predictions\n\n'
          'Use the filters to customize your view and export reports for detailed analysis.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Got it'),
          ),
        ],
      ),
    );
  }
}
