import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class LoginFormWidget extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final bool isPasswordVisible;
  final String? emailError;
  final String? passwordError;
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onTogglePasswordVisibility;
  final ValueChanged<String> onEmailChanged;
  final ValueChanged<String> onPasswordChanged;
  final VoidCallback onLogin;
  final VoidCallback onForgotPassword;

  const LoginFormWidget({
    super.key,
    required this.formKey,
    required this.emailController,
    required this.passwordController,
    required this.isPasswordVisible,
    required this.emailError,
    required this.passwordError,
    required this.isLoading,
    required this.isFormValid,
    required this.onTogglePasswordVisibility,
    required this.onEmailChanged,
    required this.onPasswordChanged,
    required this.onLogin,
    required this.onForgotPassword,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: form<PERSON><PERSON>,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Welcome Text
          Text(
            'Welcome back!',
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              color: AppTheme.textPrimaryLight,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 1),

          Text(
            'Sign in to continue your food journey',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryLight,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 4),

          // Email Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Email',
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: AppTheme.textPrimaryLight,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1),
              TextFormField(
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                enabled: !isLoading,
                onChanged: onEmailChanged,
                decoration: InputDecoration(
                  hintText: 'Enter your email address',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(12),
                    child: CustomIconWidget(
                      iconName: 'email',
                      color: AppTheme.textSecondaryLight,
                      size: 20,
                    ),
                  ),
                  errorText: null,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Your email is required to find your favorite restaurants';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Please enter a valid email to continue your food journey';
                  }
                  return null;
                },
              ),
              if (emailError != null) ...[
                SizedBox(height: 0.5),
                Text(
                  emailError!,
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.error,
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 2),

          // Password Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Password',
                style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                  color: AppTheme.textPrimaryLight,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1),
              TextFormField(
                controller: passwordController,
                obscureText: !isPasswordVisible,
                textInputAction: TextInputAction.done,
                enabled: !isLoading,
                onChanged: onPasswordChanged,
                onFieldSubmitted: (_) {
                  if (isFormValid && !isLoading) {
                    onLogin();
                  }
                },
                decoration: InputDecoration(
                  hintText: 'Enter your password',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(12),
                    child: CustomIconWidget(
                      iconName: 'lock',
                      color: AppTheme.textSecondaryLight,
                      size: 20,
                    ),
                  ),
                  suffixIcon: GestureDetector(
                    onTap: onTogglePasswordVisibility,
                    child: Padding(
                      padding: EdgeInsets.all(12),
                      child: CustomIconWidget(
                        iconName: isPasswordVisible ? 'visibility' : 'visibility_off',
                        color: AppTheme.textSecondaryLight,
                        size: 20,
                      ),
                    ),
                  ),
                  errorText: null,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Password is needed to access your delicious orders';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters for security';
                  }
                  return null;
                },
              ),
              if (passwordError != null) ...[
                SizedBox(height: 0.5),
                Text(
                  passwordError!,
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.error,
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 1),

          // Forgot Password Link
          Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              onTap: isLoading ? null : onForgotPassword,
              child: Text(
                'Forgot Password?',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.lightTheme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),

          SizedBox(height: 4),

          // Login Button
          SizedBox(
            height: 6,
            child: ElevatedButton(
              onPressed: (isFormValid && !isLoading) ? onLogin : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: isFormValid ? AppTheme.lightTheme.colorScheme.primary : AppTheme.textDisabledLight,
                foregroundColor: AppTheme.lightTheme.colorScheme.onPrimary,
                elevation: isFormValid ? 2.0 : 0.0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
              ),
              child: isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.lightTheme.colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : Text(
                      'Login',
                      style: AppTheme.lightTheme.textTheme.labelLarge?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
