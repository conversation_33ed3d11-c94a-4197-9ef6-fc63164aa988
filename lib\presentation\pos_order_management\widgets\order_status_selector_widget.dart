import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class OrderStatusSelectorWidget extends StatelessWidget {
  final String currentStatus;
  final Function(String) onStatusChanged;

  const OrderStatusSelectorWidget({
    super.key,
    required this.currentStatus,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _getStatusOptions().map((status) {
        final isSelected = currentStatus == status['value'];
        final isCompleted = _isStatusCompleted(status['value']);

        return GestureDetector(
          onTap: () => onStatusChanged(status['value']),
          child: Container(
            margin: EdgeInsets.only(bottom: 8),
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer.withAlpha(102)
                  : isCompleted
                      ? Colors.green.withAlpha(26)
                      : Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : isCompleted
                        ? Colors.green
                        : Theme.of(context).dividerColor,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.green
                        : isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isCompleted
                        ? Icons.check
                        : isSelected
                            ? status['icon']
                            : status['icon'],
                    color: Colors.white,
                    size: 14,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        status['title'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: isSelected || isCompleted
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                      ),
                      Text(
                        status['description'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Current',
                      style: GoogleFonts.inter(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                if (isCompleted && !isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Map<String, dynamic>> _getStatusOptions() {
    return [
      {
        'value': 'received',
        'title': 'Received',
        'description': 'Order has been received and confirmed',
        'icon': Icons.mail,
      },
      {
        'value': 'preparing',
        'title': 'Preparing',
        'description': 'Kitchen is preparing the order',
        'icon': Icons.kitchen,
      },
      {
        'value': 'ready',
        'title': 'Ready for Pickup',
        'description': 'Order is ready for pickup or delivery',
        'icon': Icons.check_circle,
      },
      {
        'value': 'completed',
        'title': 'Completed',
        'description': 'Order has been delivered or picked up',
        'icon': Icons.done_all,
      },
    ];
  }

  bool _isStatusCompleted(String status) {
    final currentIndex = _getStatusIndex(currentStatus);
    final statusIndex = _getStatusIndex(status);
    return statusIndex < currentIndex;
  }

  int _getStatusIndex(String status) {
    final statuses = ['received', 'preparing', 'ready', 'completed'];
    return statuses.indexOf(status);
  }
}
