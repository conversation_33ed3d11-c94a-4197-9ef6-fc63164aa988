import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class LocationPermissionWidget extends StatelessWidget {
  final bool isRequested;
  final VoidCallback onRequestPermission;

  const LocationPermissionWidget({
    super.key,
    required this.isRequested,
    required this.onRequestPermission,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.accentLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.accentLight.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: AppTheme.accentLight,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: 'location_on',
                  color: Colors.white,
                  size: 5,
                ),
              ),
              SizedBox(width: 3),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enable Location',
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryLight,
                      ),
                    ),
                    SizedBox(height: 0.5),
                    Text(
                      'We need your location to show nearby restaurants and provide accurate delivery estimates.',
                      style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryLight,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 2),

          // Benefits list
          Column(
            children: [
              _buildBenefitItem(
                icon: 'restaurant',
                text: 'Find restaurants near you',
              ),
              SizedBox(height: 1),
              _buildBenefitItem(
                icon: 'delivery_dining',
                text: 'Get accurate delivery times',
              ),
              SizedBox(height: 1),
              _buildBenefitItem(
                icon: 'track_changes',
                text: 'Track your orders in real-time',
              ),
            ],
          ),

          SizedBox(height: 3),

          // Allow location button
          SizedBox(
            width: double.infinity,
            height: 6,
            child: ElevatedButton(
              onPressed: isRequested ? null : onRequestPermission,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentLight,
                foregroundColor: Colors.white,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: isRequested
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 4,
                          height: 4,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 3),
                        Text(
                          'Requesting Permission...',
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomIconWidget(
                          iconName: 'location_on',
                          color: Colors.white,
                          size: 5,
                        ),
                        SizedBox(width: 2),
                        Text(
                          'Allow Location Access',
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),

          SizedBox(height: 2),

          // Skip option
          Center(
            child: TextButton(
              onPressed: () {
                // Handle skip location permission
              },
              child: Text(
                'Skip for now',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryLight,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem({
    required String icon,
    required String text,
  }) {
    return Row(
      children: [
        CustomIconWidget(
          iconName: icon,
          color: AppTheme.accentLight,
          size: 4,
        ),
        SizedBox(width: 3),
        Expanded(
          child: Text(
            text,
            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryLight,
            ),
          ),
        ),
      ],
    );
  }
}
