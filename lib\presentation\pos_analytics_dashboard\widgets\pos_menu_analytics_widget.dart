import 'package:flutter/material.dart';

class PosMenuAnalyticsWidget extends StatelessWidget {
  const PosMenuAnalyticsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Menu Performance Analysis',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 16),
            _buildMenuSection(
              'Best Sellers',
              Icons.trending_up,
              Colors.green,
              [
                _buildMenuItemRow('Margherita Pizza', '127 sold', '+15%'),
                _buildMenuItemRow('Caesar Salad', '98 sold', '+8%'),
                _buildMenuItemRow('Chicken Burger', '85 sold', '+12%'),
              ],
            ),
            SizedBox(height: 16),
            _buildMenuSection(
              'Underperforming Items',
              Icons.trending_down,
              Colors.red,
              [
                _buildMenuItemRow('Seafood Pasta', '12 sold', '-25%'),
                _buildMenuItemRow('Vegetarian Wrap', '8 sold', '-18%'),
                _buildMenuItemRow('Soup of the Day', '5 sold', '-30%'),
              ],
            ),
            SizedBox(height: 16),
            _buildMenuSection(
              'High Profit Margin',
              Icons.attach_money,
              Colors.blue,
              [
                _buildMenuItemRow('Fresh Juice', '65% margin', '+5%'),
                _buildMenuItemRow('Coffee', '70% margin', '+3%'),
                _buildMenuItemRow('Dessert Platter', '60% margin', '+8%'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuSection(String title, IconData icon, Color color, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        ...items,
      ],
    );
  }

  Widget _buildMenuItemRow(String name, String metric, String trend) {
    bool isPositive = trend.startsWith('+');
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              name,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            metric,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(width: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: isPositive ? Colors.green.withAlpha(26) : Colors.red.withAlpha(26),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              trend,
              style: TextStyle(
                fontSize: 12,
                color: isPositive ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
