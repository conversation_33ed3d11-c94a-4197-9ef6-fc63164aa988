import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import './order_status_selector_widget.dart';

class OrderDetailWidget extends StatelessWidget {
  final String orderId;
  final Function(String, String) onStatusUpdate;
  final ScrollController? scrollController;

  const OrderDetailWidget({
    super.key,
    required this.orderId,
    required this.onStatusUpdate,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    final order = _getOrderById(orderId);
    if (order == null) {
      return Center(
        child: Text('Order not found'),
      );
    }

    return Column(
      children: [
        if (scrollController != null) _buildDragHandle(),
        _buildHeader(context, order),
        Expanded(
          child: SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCustomerInfo(context, order),
                SizedBox(height: 16),
                _buildOrderItems(context, order),
                SizedBox(height: 16),
                _buildSpecialInstructions(context, order),
                SizedBox(height: 16),
                _buildOrderTiming(context, order),
                SizedBox(height: 16),
                _buildOrderActions(context, order),
                SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, Map<String, dynamic> order) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              order['id'],
              style: GoogleFonts.inter(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Details',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Text(
                  'Ordered at ${order['orderTime']} • ${order['deliveryMethod']}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context, Map<String, dynamic> order) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Customer Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildInfoRow(context, 'Name', order['customerName']),
            _buildInfoRow(context, 'Phone', order['customerPhone']),
            _buildInfoRow(context, 'Payment', order['paymentStatus'].toString().toUpperCase()),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItems(BuildContext context, Map<String, dynamic> order) {
    final items = order['items'] as List;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.restaurant_menu,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Order Items',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Spacer(),
                Text(
                  '\$${order['totalAmount'].toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
              ],
            ),
            SizedBox(height: 12),
            ...items.map((item) => _buildOrderItem(context, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, Map<String, dynamic> item) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                '${item['quantity']}',
                style: GoogleFonts.inter(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name'],
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                if (item['notes'].toString().isNotEmpty)
                  Text(
                    'Note: ${item['notes']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange[700],
                          fontStyle: FontStyle.italic,
                        ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialInstructions(BuildContext context, Map<String, dynamic> order) {
    if (order['specialInstructions'].toString().isEmpty) {
      return SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_alt,
                  color: Colors.orange,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Special Instructions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                order['specialInstructions'],
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderTiming(BuildContext context, Map<String, dynamic> order) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Timing Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildInfoRow(context, 'Order Time', order['orderTime']),
            _buildInfoRow(context, 'Estimated Ready', order['estimatedTime']),
            _buildInfoRow(context, 'Priority', order['priority'].toString().toUpperCase()),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderActions(BuildContext context, Map<String, dynamic> order) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Update Order Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 12),
            OrderStatusSelectorWidget(
              currentStatus: order['status'],
              onStatusChanged: (newStatus) {
                onStatusUpdate(orderId, newStatus);
              },
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {},
                    icon: Icon(Icons.phone),
                    label: Text('Call Customer'),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {},
                    icon: Icon(Icons.edit),
                    label: Text('Modify Order'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic>? _getOrderById(String id) {
    final orders = [
      {
        'id': 'ORD001',
        'customerName': 'John Doe',
        'customerPhone': '****** 567 8900',
        'items': [
          {'name': 'Margherita Pizza', 'quantity': 2, 'notes': 'Extra cheese'},
          {'name': 'Caesar Salad', 'quantity': 1, 'notes': ''},
        ],
        'totalAmount': 45.99,
        'orderTime': '14:30',
        'estimatedTime': '15:00',
        'status': 'received',
        'priority': 'urgent',
        'deliveryMethod': 'delivery',
        'specialInstructions': 'Ring doorbell twice, leave at door',
        'paymentStatus': 'paid',
      },
      {
        'id': 'ORD002',
        'customerName': 'Jane Smith',
        'customerPhone': '****** 567 8901',
        'items': [
          {'name': 'Chicken Burger', 'quantity': 1, 'notes': 'No pickles'},
          {'name': 'French Fries', 'quantity': 1, 'notes': 'Extra crispy'},
          {'name': 'Coke', 'quantity': 2, 'notes': ''},
        ],
        'totalAmount': 28.50,
        'orderTime': '14:25',
        'estimatedTime': '14:50',
        'status': 'preparing',
        'priority': 'standard',
        'deliveryMethod': 'pickup',
        'specialInstructions': '',
        'paymentStatus': 'paid',
      },
      {
        'id': 'ORD003',
        'customerName': 'Mike Johnson',
        'customerPhone': '****** 567 8902',
        'items': [
          {'name': 'Grilled Salmon', 'quantity': 1, 'notes': 'Medium rare'},
          {'name': 'Steamed Vegetables', 'quantity': 1, 'notes': ''},
        ],
        'totalAmount': 32.00,
        'orderTime': '15:00',
        'estimatedTime': '15:30',
        'status': 'ready',
        'priority': 'scheduled',
        'deliveryMethod': 'dine-in',
        'specialInstructions': 'Table 5',
        'paymentStatus': 'paid',
      },
    ];

    return orders.firstWhere((order) => order['id'] == id, orElse: () => {});
  }
}
