import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class GeographicDistributionWidget extends StatelessWidget {
  const GeographicDistributionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Geographic Distribution',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                TextButton.icon(
                  onPressed: () {
                    // Switch to map view
                  },
                  icon: Icon(Icons.map, size: 16),
                  label: Text('Map View'),
                ),
              ],
            ),
            SizedBox(height: 20),
            _buildDeliveryZonesList(context),
            SizedBox(height: 20),
            _buildPerformanceMetrics(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryZonesList(BuildContext context) {
    final zones = [
      {
        'name': 'Downtown',
        'orders': 2547,
        'revenue': 78420,
        'avgTime': '28 min',
        'growth': '+15.3%',
        'isPositive': true
      },
      {
        'name': 'University Area',
        'orders': 1892,
        'revenue': 52340,
        'avgTime': '32 min',
        'growth': '+22.1%',
        'isPositive': true
      },
      {
        'name': 'Business District',
        'orders': 1634,
        'revenue': 89560,
        'avgTime': '25 min',
        'growth': '+8.7%',
        'isPositive': true
      },
      {
        'name': 'Residential North',
        'orders': 1456,
        'revenue': 41250,
        'avgTime': '35 min',
        'growth': '-2.4%',
        'isPositive': false
      },
      {
        'name': 'Suburban West',
        'orders': 1023,
        'revenue': 35890,
        'avgTime': '42 min',
        'growth': '+5.6%',
        'isPositive': true
      },
    ];

    return Column(
      children: zones.map((zone) => _buildZoneItem(context, zone)).toList(),
    );
  }

  Widget _buildZoneItem(BuildContext context, Map<String, dynamic> zone) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(77),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  zone['name'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: zone['isPositive'] ? Colors.green.withAlpha(26) : Colors.red.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      zone['isPositive'] ? Icons.trending_up : Icons.trending_down,
                      size: 14,
                      color: zone['isPositive'] ? Colors.green : Colors.red,
                    ),
                    SizedBox(width: 4),
                    Text(
                      zone['growth'],
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: zone['isPositive'] ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildZoneMetric(context, 'Orders', '${zone['orders']}', Icons.shopping_cart_outlined),
              ),
              Expanded(
                child: _buildZoneMetric(
                    context, 'Revenue', '\$${(zone['revenue'] / 1000).toStringAsFixed(1)}K', Icons.attach_money),
              ),
              Expanded(
                child: _buildZoneMetric(context, 'Avg Time', zone['avgTime'], Icons.schedule),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildZoneMetric(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
      ],
    );
  }

  Widget _buildPerformanceMetrics(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withAlpha(13),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zone Performance Insights',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 12),
          _buildInsightItem(
            context,
            'Top Performing Zone',
            'Business District - Highest revenue per order (\$54.80)',
            Icons.star,
            Colors.orange,
          ),
          SizedBox(height: 8),
          _buildInsightItem(
            context,
            'Fastest Delivery',
            'Business District - Average 25 minutes',
            Icons.speed,
            Colors.green,
          ),
          SizedBox(height: 8),
          _buildInsightItem(
            context,
            'Growth Leader',
            'University Area - 22.1% order growth',
            Icons.trending_up,
            Colors.blue,
          ),
          SizedBox(height: 8),
          _buildInsightItem(
            context,
            'Needs Attention',
            'Residential North - Declining orders, consider promotions',
            Icons.warning,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(BuildContext context, String title, String description, IconData icon, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
