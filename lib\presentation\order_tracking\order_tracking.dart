import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/app_export.dart';
import './widgets/delivery_map_widget.dart';
import './widgets/driver_info_widget.dart';
import './widgets/order_status_header_widget.dart';
import './widgets/order_summary_widget.dart';
import './widgets/restaurant_info_widget.dart';

class OrderTracking extends StatefulWidget {
  const OrderTracking({super.key});

  @override
  State<OrderTracking> createState() => _OrderTrackingState();
}

class _OrderTrackingState extends State<OrderTracking> with TickerProviderStateMixin {
  late AnimationController _refreshAnimationController;
  late ScrollController _scrollController;

  bool _isLoading = false;
  bool _showRatingDialog = false;

  // Mock order data
  final Map<String, dynamic> _orderData = {
    'orderNumber': '12345',
    'status': 'out_for_delivery',
    'restaurantName': 'Bella Vista Italian',
    'estimatedTime': '15-20 min',
    'subtotal': '\$32.50',
    'deliveryFee': '\$2.99',
    'serviceFee': '\$1.50',
    'tax': '\$3.25',
    'discount': '\$5.00',
    'total': '\$35.24',
    'paymentMethod': 'Credit Card ****1234',
    'deliveryAddress': '123 Main St, Apt 4B, New York, NY 10001',
    'placedAt': '2024-01-15T18:30:00Z',
  };

  final Map<String, dynamic> _restaurantData = {
    'name': 'Bella Vista Italian',
    'image':
        'https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    'rating': 4.8,
    'cuisine': 'Italian • Mediterranean',
    'address': '456 Restaurant Ave, New York, NY 10002',
    'phone': '+****************',
  };

  final Map<String, dynamic> _driverData = {
    'name': 'Alex Rodriguez',
    'photo':
        'https://images.pixabay.com/photos/2507563/pexels-photo-2507563.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    'rating': 4.9,
    'totalDeliveries': 1250,
    'phone': '+****************',
    'vehicle': {'make': 'Honda', 'model': 'Civic', 'color': 'Blue', 'licensePlate': 'ABC-1234'},
    'currentLocation': 'Oak Street, 2 blocks away',
    'lastUpdate': '2 mins ago',
  };

  final Map<String, dynamic> _driverLocation = {
    'latitude': 40.7128,
    'longitude': -74.0060,
    'distance': '0.8 mi',
    'eta': '8 min',
  };

  final Map<String, dynamic> _deliveryAddress = {
    'latitude': 40.7589,
    'longitude': -73.9851,
    'address': '123 Main St, Apt 4B, New York, NY 10001',
  };

  final List<Map<String, dynamic>> _orderItems = [
    {
      'id': 1,
      'name': 'Spaghetti Carbonara',
      'image':
          'https://images.pexels.com/photos/4518843/pexels-photo-4518843.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'price': '\$18.99',
      'quantity': 1,
      'customization': {
        'size': 'Regular',
        'extras': ['Extra Parmesan'],
        'notes': 'No black pepper'
      }
    },
    {
      'id': 2,
      'name': 'Margherita Pizza',
      'image':
          'https://images.pexels.com/photos/315755/pexels-photo-315755.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'price': '\$16.99',
      'quantity': 1,
      'customization': {
        'size': 'Medium',
        'extras': ['Extra Basil'],
      }
    },
    {
      'id': 3,
      'name': 'Italian Soda',
      'image':
          'https://images.pexels.com/photos/1283219/pexels-photo-1283219.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      'price': '\$3.99',
      'quantity': 2,
      'customization': {
        'flavor': 'Lemon',
      }
    },
  ];

  @override
  void initState() {
    super.initState();
    _refreshAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scrollController = ScrollController();
    _startLocationUpdates();
  }

  @override
  void dispose() {
    _refreshAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _startLocationUpdates() {
    // Simulate periodic location updates
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        _triggerLocationUpdate();
        _startLocationUpdates();
      }
    });
  }

  void _triggerLocationUpdate() {
    if (mounted) {
      setState(() {
        // Update mock location data
        _driverLocation['distance'] = '0.6 mi';
        _driverLocation['eta'] = '6 min';
        _driverData['lastUpdate'] = 'Just now';
      });

      // Show notification
      _showUpdateNotification('Driver location updated');

      // Haptic feedback
      HapticFeedback.lightImpact();
    }
  }

  void _showUpdateNotification(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            CustomIconWidget(
              iconName: 'location_on',
              size: 20,
              color: Colors.white,
            ),
            SizedBox(width: 2),
            Text(message),
          ],
        ),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _refreshOrderStatus() async {
    setState(() {
      _isLoading = true;
    });

    _refreshAnimationController.forward();

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    _refreshAnimationController.reverse();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      _showUpdateNotification('Order status refreshed');
    }
  }

  void _callRestaurant() {
    _showActionSheet(
      title: 'Call Restaurant',
      message: 'Call ${_restaurantData['name']} at ${_restaurantData['phone']}?',
      onConfirm: () {
        // Implement phone call
        Navigator.pop(context);
        _showUpdateNotification('Calling restaurant...');
      },
    );
  }

  void _callDriver() {
    _showActionSheet(
      title: 'Call Driver',
      message: 'Call ${_driverData['name']} at ${_driverData['phone']}?',
      onConfirm: () {
        // Implement phone call
        Navigator.pop(context);
        _showUpdateNotification('Calling driver...');
      },
    );
  }

  void _messageDriver() {
    _showChatBottomSheet();
  }

  void _showChatBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 70,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                  ),
                ),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: CustomImageWidget(
                      imageUrl: _driverData['photo'] as String,
                      width: 10,
                      height: 10,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 3),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _driverData['name'] as String,
                          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Your delivery driver',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: CustomIconWidget(
                      iconName: 'close',
                      size: 24,
                      color: AppTheme.lightTheme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.all(4),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'chat',
                      size: 48,
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(height: 2),
                    Text(
                      'Start a conversation',
                      style: AppTheme.lightTheme.textTheme.titleMedium,
                    ),
                    SizedBox(height: 1),
                    Text(
                      'Send a message to your driver for any special instructions or updates.',
                      textAlign: TextAlign.center,
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Type a message...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 2),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _showUpdateNotification('Message sent to driver');
                      },
                      icon: CustomIconWidget(
                        iconName: 'send',
                        size: 20,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullScreenMap() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _FullScreenMapView(
          driverLocation: _driverLocation,
          deliveryAddress: _deliveryAddress,
          driverData: _driverData,
        ),
      ),
    );
  }

  void _contactSupport() {
    _showActionSheet(
      title: 'Contact Support',
      message: 'Need help with your order? Our support team is here to assist you.',
      onConfirm: () {
        Navigator.pop(context);
        _showUpdateNotification('Connecting to support...');
      },
      confirmText: 'Contact',
    );
  }

  void _showActionSheet({
    required String title,
    required String message,
    required VoidCallback onConfirm,
    String confirmText = 'Call',
  }) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: AppTheme.lightTheme.textTheme.titleLarge,
            ),
            SizedBox(height: 2),
            Text(
              message,
              textAlign: TextAlign.center,
              style: AppTheme.lightTheme.textTheme.bodyMedium,
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Cancel'),
                  ),
                ),
                SizedBox(width: 3),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onConfirm,
                    child: Text(confirmText),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _checkDeliveryCompletion() {
    if (_orderData['status'] == 'delivered' && !_showRatingDialog) {
      _showRatingDialog = true;
      _showDeliveryCompletionDialog();
    }
  }

  void _showDeliveryCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('Order Delivered!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomIconWidget(
              iconName: 'check_circle',
              size: 64,
              color: AppTheme.lightTheme.colorScheme.tertiary,
            ),
            SizedBox(height: 2),
            Text(
              'Your order has been successfully delivered. How was your experience?',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, AppRoutes.restaurantBrowse);
            },
            child: Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showRatingScreen();
            },
            child: Text('Rate Order'),
          ),
        ],
      ),
    );
  }

  void _showRatingScreen() {
    // Navigate to rating screen or show rating modal
    _showUpdateNotification('Thank you for your feedback!');
    Navigator.pushReplacementNamed(context, AppRoutes.restaurantBrowse);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Track Order'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          AnimatedBuilder(
            animation: _refreshAnimationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _refreshAnimationController.value * 2 * 3.14159,
                child: IconButton(
                  onPressed: _isLoading ? null : _refreshOrderStatus,
                  icon: CustomIconWidget(
                    iconName: 'refresh',
                    color: _isLoading
                        ? AppTheme.lightTheme.colorScheme.onSurfaceVariant
                        : AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshOrderStatus,
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(4),
          child: Column(
            children: [
              // Order Status Header
              OrderStatusHeaderWidget(
                orderData: _orderData,
                onStatusChanged: (status) {
                  setState(() {
                    _orderData['status'] = status;
                  });
                  _checkDeliveryCompletion();
                },
              ),
              SizedBox(height: 3),

              // Restaurant Info
              RestaurantInfoWidget(
                restaurantData: _restaurantData,
                onCallRestaurant: _callRestaurant,
                onViewMenu: () {
                  Navigator.pushNamed(context, AppRoutes.restaurantMenu);
                },
              ),
              SizedBox(height: 3),

              // Delivery Map
              DeliveryMapWidget(
                driverLocation: _driverLocation,
                deliveryAddress: _deliveryAddress,
                onFullScreenMap: _openFullScreenMap,
                onRefreshLocation: () {
                  _triggerLocationUpdate();
                },
              ),
              SizedBox(height: 3),

              // Driver Info
              DriverInfoWidget(
                driverData: _driverData,
                onCallDriver: _callDriver,
                onMessageDriver: _messageDriver,
              ),
              SizedBox(height: 3),

              // Order Summary
              OrderSummaryWidget(
                orderItems: _orderItems,
                orderData: _orderData,
                onContactSupport: _contactSupport,
              ),
              SizedBox(height: 10),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _openFullScreenMap,
        icon: CustomIconWidget(
          iconName: 'map',
          size: 24,
          color: Colors.white,
        ),
        label: Text('Track Driver'),
        backgroundColor: AppTheme.lightTheme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
    );
  }
}

class _FullScreenMapView extends StatelessWidget {
  final Map<String, dynamic> driverLocation;
  final Map<String, dynamic> deliveryAddress;
  final Map<String, dynamic> driverData;

  const _FullScreenMapView({
    required this.driverLocation,
    required this.deliveryAddress,
    required this.driverData,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Track Driver'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // Center map on driver location
            },
            icon: CustomIconWidget(
              iconName: 'my_location',
              color: AppTheme.lightTheme.colorScheme.onSurface,
              size: 24,
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Full screen map
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.green.shade50,
                ],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'map',
                    size: 64,
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                  SizedBox(height: 2),
                  Text(
                    'Full Screen Map View',
                    style: AppTheme.lightTheme.textTheme.titleLarge,
                  ),
                  SizedBox(height: 1),
                  Text(
                    'Driver is ${driverLocation['distance']} away',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom driver info card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.lightTheme.colorScheme.shadow,
                    blurRadius: 16,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 1,
                    decoration: BoxDecoration(
                      color: AppTheme.lightTheme.colorScheme.outline.withAlpha(77),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  SizedBox(height: 2),
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CustomImageWidget(
                          imageUrl: driverData['photo'] as String,
                          width: 16,
                          height: 16,
                          fit: BoxFit.cover,
                        ),
                      ),
                      SizedBox(width: 3),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              driverData['name'] as String,
                              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${driverLocation['distance']} away • ${driverLocation['eta']} ETA',
                              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppTheme.lightTheme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          onPressed: () {
                            // Call driver
                          },
                          icon: CustomIconWidget(
                            iconName: 'phone',
                            size: 20,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
