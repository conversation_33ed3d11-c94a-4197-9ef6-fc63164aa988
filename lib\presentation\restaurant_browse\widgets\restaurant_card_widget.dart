import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class RestaurantCardWidget extends StatelessWidget {
  final Map<String, dynamic> restaurant;
  final VoidCallback onTap;
  final VoidCallback onFavorite;

  const RestaurantCardWidget({
    super.key,
    required this.restaurant,
    required this.onTap,
    required this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 3),
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.lightTheme.shadowColor,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Restaurant image with badges and favorite button
            _buildImageSection(),

            // Restaurant details
            _buildDetailsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        // Restaurant image
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: CustomImageWidget(
            imageUrl: restaurant['image'] as String,
            width: double.infinity,
            height: 20,
            fit: BoxFit.cover,
          ),
        ),

        // Badges
        if ((restaurant['badges'] as List).isNotEmpty)
          Positioned(
            top: 2,
            left: 3,
            child: _buildBadges(),
          ),

        // Favorite button
        Positioned(
          top: 2,
          right: 3,
          child: GestureDetector(
            onTap: onFavorite,
            child: Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: (restaurant['isFavorite'] as bool) ? 'favorite' : 'favorite_border',
                color: (restaurant['isFavorite'] as bool)
                    ? AppTheme.lightTheme.primaryColor
                    : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBadges() {
    final badges = restaurant['badges'] as List;
    return Wrap(
      spacing: 2,
      children: badges.map((badge) {
        Color badgeColor;
        switch (badge as String) {
          case 'Free Delivery':
            badgeColor = AppTheme.lightTheme.colorScheme.tertiary;
            break;
          case 'New Restaurant':
            badgeColor = AppTheme.lightTheme.primaryColor;
            break;
          case 'Premium':
            badgeColor = AppTheme.lightTheme.colorScheme.secondary;
            break;
          default:
            badgeColor = AppTheme.lightTheme.colorScheme.secondary;
        }

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0.5),
          decoration: BoxDecoration(
            color: badgeColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            badge,
            style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
              color: AppTheme.lightTheme.colorScheme.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDetailsSection() {
    return Padding(
      padding: EdgeInsets.all(4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Restaurant name and cuisine
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      restaurant['name'] as String,
                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 0.5),
                    Text(
                      restaurant['cuisine'] as String,
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              // Rating
              _buildRatingSection(),
            ],
          ),

          SizedBox(height: 2),

          // Delivery info
          Row(
            children: [
              // Delivery time
              CustomIconWidget(
                iconName: 'access_time',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              SizedBox(width: 1),
              Text(
                restaurant['deliveryTime'] as String,
                style: AppTheme.lightTheme.textTheme.bodySmall,
              ),

              SizedBox(width: 4),

              // Delivery fee
              CustomIconWidget(
                iconName: 'delivery_dining',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              SizedBox(width: 1),
              Text(
                restaurant['deliveryFee'] as String,
                style: AppTheme.lightTheme.textTheme.bodySmall,
              ),

              const Spacer(),

              // Distance
              CustomIconWidget(
                iconName: 'location_on',
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 16,
              ),
              SizedBox(width: 1),
              Text(
                restaurant['distance'] as String,
                style: AppTheme.lightTheme.textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSection() {
    final rating = restaurant['rating'] as double;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2, vertical: 1),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.tertiary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomIconWidget(
            iconName: 'star',
            color: AppTheme.lightTheme.colorScheme.tertiary,
            size: 16,
          ),
          SizedBox(width: 1),
          Text(
            rating.toString(),
            style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.lightTheme.colorScheme.tertiary,
            ),
          ),
        ],
      ),
    );
  }
}
