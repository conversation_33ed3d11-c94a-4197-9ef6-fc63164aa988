import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/app_export.dart';

class CartItemWidget extends StatefulWidget {
  final Map<String, dynamic> item;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final Function(int) onQuantityChanged;

  const CartItemWidget({
    super.key,
    required this.item,
    required this.onEdit,
    required this.onDelete,
    required this.onQuantityChanged,
  });

  @override
  State<CartItemWidget> createState() => _CartItemWidgetState();
}

class _CartItemWidgetState extends State<CartItemWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  bool _isSwipeRevealed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-0.3, 0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleSwipe() {
    if (_isSwipeRevealed) {
      _animationController.reverse();
    } else {
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
    setState(() {
      _isSwipeRevealed = !_isSwipeRevealed;
    });
  }

  void _onDeleteTap() {
    HapticFeedback.mediumImpact();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Remove Item'),
        content: Text('Are you sure you want to remove ${widget.item['name']} from your cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightTheme.colorScheme.error,
            ),
            child: Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _updateQuantity(int change) {
    final currentQuantity = widget.item['quantity'] as int;
    final newQuantity = currentQuantity + change;
    if (newQuantity > 0) {
      HapticFeedback.selectionClick();
      widget.onQuantityChanged(newQuantity);
    }
  }

  @override
  Widget build(BuildContext context) {
    final quantity = widget.item['quantity'] as int;
    final price = double.parse((widget.item['price'] as String).replaceAll('\$', ''));
    final totalPrice = price * quantity;
    final customization = widget.item['customization'] as Map<String, dynamic>?;

    return GestureDetector(
      onTap: widget.onEdit,
      onHorizontalDragUpdate: (details) {
        if (details.delta.dx < -5) {
          if (!_isSwipeRevealed) _toggleSwipe();
        } else if (details.delta.dx > 5) {
          if (_isSwipeRevealed) _toggleSwipe();
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        child: Stack(
          children: [
            // Delete action background
            Container(
              height: 20,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.error,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 6),
                    child: GestureDetector(
                      onTap: _onDeleteTap,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomIconWidget(
                            iconName: 'delete',
                            color: Colors.white,
                            size: 28,
                          ),
                          SizedBox(height: 1),
                          Text(
                            'Delete',
                            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Main card content
            SlideTransition(
              position: _slideAnimation,
              child: Container(
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.lightTheme.colorScheme.shadow,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(3),
                  child: Row(
                    children: [
                      // Food image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CustomImageWidget(
                          imageUrl: widget.item['image'] as String,
                          width: 25,
                          height: 14,
                          fit: BoxFit.cover,
                        ),
                      ),
                      SizedBox(width: 3),
                      // Item details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.item['name'] as String,
                              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (customization != null &&
                                customization.containsKey('specialInstructions') &&
                                (customization['specialInstructions'] as String).isNotEmpty) ...[
                              SizedBox(height: 0.5),
                              Text(
                                'Special: ${customization['specialInstructions']}',
                                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                            const Spacer(),
                            // Price
                            Text(
                              '\$${totalPrice.toStringAsFixed(2)}',
                              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppTheme.lightTheme.colorScheme.primary,
                              ),
                            ),
                            SizedBox(height: 1),
                            // Quantity controls
                            Row(
                              children: [
                                GestureDetector(
                                  onTap: () => _updateQuantity(-1),
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: quantity > 1
                                          ? AppTheme.lightTheme.colorScheme.primary
                                          : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                      shape: BoxShape.circle,
                                    ),
                                    child: CustomIconWidget(
                                      iconName: 'remove',
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                                Container(
                                  width: 12,
                                  alignment: Alignment.center,
                                  child: Text(
                                    quantity.toString(),
                                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () => _updateQuantity(1),
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: AppTheme.lightTheme.colorScheme.primary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: CustomIconWidget(
                                      iconName: 'add',
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // Edit indicator
                      Column(
                        children: [
                          CustomIconWidget(
                            iconName: 'edit',
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                            size: 20,
                          ),
                          Text(
                            'Edit',
                            style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                              color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
