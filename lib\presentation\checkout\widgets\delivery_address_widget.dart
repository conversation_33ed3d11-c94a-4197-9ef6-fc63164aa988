import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class DeliveryAddressWidget extends StatelessWidget {
  final Map<String, dynamic> currentAddress;
  final VoidCallback onChangeAddress;

  const DeliveryAddressWidget({
    super.key,
    required this.currentAddress,
    required this.onChangeAddress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(color: AppTheme.lightTheme.colorScheme.shadow, blurRadius: 4, offset: const Offset(0, 2)),
            ]),
        child: InkWell(
            onTap: onChangeAddress,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
                padding: EdgeInsets.all(4),
                child: <PERSON>umn(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(children: [
                    Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                            color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                            borderRadius: BorderRadius.circular(8)),
                        child: CustomIconWidget(
                            iconName: 'location_on', color: AppTheme.lightTheme.colorScheme.primary, size: 20)),
                    SizedBox(width: 3),
                    Expanded(
                        child: Text('Delivery Address',
                            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600))),
                    Container(
                        padding: EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                        decoration: BoxDecoration(
                            color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                            borderRadius: BorderRadius.circular(20)),
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                          Text('Change',
                              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                                  color: AppTheme.lightTheme.colorScheme.primary, fontWeight: FontWeight.w600)),
                          SizedBox(width: 1),
                          CustomIconWidget(
                              iconName: 'arrow_forward', color: AppTheme.lightTheme.colorScheme.primary, size: 14),
                        ])),
                  ]),
                  SizedBox(height: 2),
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Container(
                        margin: EdgeInsets.only(top: 0.5),
                        padding: EdgeInsets.all(1),
                        decoration: BoxDecoration(
                            color: _getAddressTypeColor(currentAddress['type']).withAlpha(51),
                            borderRadius: BorderRadius.circular(4)),
                        child: CustomIconWidget(
                            iconName: _getAddressTypeIcon(currentAddress['type']),
                            color: _getAddressTypeColor(currentAddress['type']),
                            size: 14)),
                    SizedBox(width: 3),
                    Expanded(
                        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Row(children: [
                        Text(currentAddress['type'] as String,
                            style: AppTheme.lightTheme.textTheme.labelMedium?.copyWith(
                                fontWeight: FontWeight.w600, color: _getAddressTypeColor(currentAddress['type']))),
                        if (currentAddress['isDefault'] == true) ...[
                          SizedBox(width: 2),
                          Container(
                              padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0.5),
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
                              child: Text('Default',
                                  style:
                                      AppTheme.lightTheme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500))),
                        ],
                      ]),
                      SizedBox(height: 0.5),
                      Text(currentAddress['fullAddress'] as String,
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurface)),
                      if (currentAddress['unit'] != null && (currentAddress['unit'] as String).isNotEmpty) ...[
                        SizedBox(height: 0.5),
                        Text('Unit: ${currentAddress['unit']}',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(color: AppTheme.lightTheme.colorScheme.onSurfaceVariant)),
                      ],
                      SizedBox(height: 1),
                      Row(children: [
                        CustomIconWidget(iconName: 'access_time', size: 14),
                        SizedBox(width: 1),
                        Text('Est. delivery: ${currentAddress['estimatedDelivery']}',
                            style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w500)),
                      ]),
                    ])),
                  ]),
                ]))));
  }

  String _getAddressTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'home':
        return 'home';
      case 'work':
        return 'work';
      case 'other':
        return 'place';
      default:
        return 'location_on';
    }
  }

  Color _getAddressTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'home':
        return Colors.green;
      case 'work':
        return AppTheme.lightTheme.colorScheme.primary;
      case 'other':
        return Colors.orange;
      default:
        return AppTheme.lightTheme.colorScheme.onSurfaceVariant;
    }
  }
}
