import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import './order_card_widget.dart';

class OrderQueueWidget extends StatelessWidget {
  final String filter;
  final String selectedOrderId;
  final Function(String) onOrderSelected;

  const OrderQueueWidget({
    super.key,
    required this.filter,
    required this.selectedOrderId,
    required this.onOrderSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.queue,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Order Queue',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_getFilteredOrders().length} orders',
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(8),
            itemCount: _getFilteredOrders().length,
            itemBuilder: (context, index) {
              final order = _getFilteredOrders()[index];
              return OrderCardWidget(
                order: order,
                isSelected: selectedOrderId == order['id'],
                onTap: () => onOrderSelected(order['id']),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getFilteredOrders() {
    final allOrders = _getMockOrders();

    if (filter == 'All') {
      return allOrders;
    }

    return allOrders.where((order) {
      switch (filter) {
        case 'Urgent':
          return order['priority'] == 'urgent';
        case 'Standard':
          return order['priority'] == 'standard';
        case 'Scheduled':
          return order['priority'] == 'scheduled';
        case 'Dine-in':
          return order['deliveryMethod'] == 'dine-in';
        case 'Delivery':
          return order['deliveryMethod'] == 'delivery';
        case 'Pickup':
          return order['deliveryMethod'] == 'pickup';
        default:
          return true;
      }
    }).toList();
  }

  List<Map<String, dynamic>> _getMockOrders() {
    return [
      {
        'id': 'ORD001',
        'customerName': 'John Doe',
        'customerPhone': '****** 567 8900',
        'items': [
          {'name': 'Margherita Pizza', 'quantity': 2, 'notes': 'Extra cheese'},
          {'name': 'Caesar Salad', 'quantity': 1, 'notes': ''},
        ],
        'totalAmount': 45.99,
        'orderTime': '14:30',
        'estimatedTime': '15:00',
        'status': 'received',
        'priority': 'urgent',
        'deliveryMethod': 'delivery',
        'specialInstructions': 'Ring doorbell twice, leave at door',
        'paymentStatus': 'paid',
      },
      {
        'id': 'ORD002',
        'customerName': 'Jane Smith',
        'customerPhone': '****** 567 8901',
        'items': [
          {'name': 'Chicken Burger', 'quantity': 1, 'notes': 'No pickles'},
          {'name': 'French Fries', 'quantity': 1, 'notes': 'Extra crispy'},
          {'name': 'Coke', 'quantity': 2, 'notes': ''},
        ],
        'totalAmount': 28.50,
        'orderTime': '14:25',
        'estimatedTime': '14:50',
        'status': 'preparing',
        'priority': 'standard',
        'deliveryMethod': 'pickup',
        'specialInstructions': '',
        'paymentStatus': 'paid',
      },
      {
        'id': 'ORD003',
        'customerName': 'Mike Johnson',
        'customerPhone': '****** 567 8902',
        'items': [
          {'name': 'Grilled Salmon', 'quantity': 1, 'notes': 'Medium rare'},
          {'name': 'Steamed Vegetables', 'quantity': 1, 'notes': ''},
        ],
        'totalAmount': 32.00,
        'orderTime': '15:00',
        'estimatedTime': '15:30',
        'status': 'ready',
        'priority': 'scheduled',
        'deliveryMethod': 'dine-in',
        'specialInstructions': 'Table 5',
        'paymentStatus': 'paid',
      },
    ];
  }
}
