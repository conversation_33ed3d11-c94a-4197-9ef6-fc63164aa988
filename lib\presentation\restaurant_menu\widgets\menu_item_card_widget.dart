import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class MenuItemCardWidget extends StatelessWidget {
  final Map<String, dynamic> item;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final String searchQuery;
  final bool isInCart;

  const MenuItemCardWidget({
    super.key,
    required this.item,
    required this.onTap,
    required this.onLongPress,
    required this.searchQuery,
    required this.isInCart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(3),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Item image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CustomImageWidget(
                    imageUrl: item['image'] as String,
                    width: 20,
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                ),

                SizedBox(width: 3),

                // Item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildHighlightedText(
                              item['name'] as String,
                              searchQuery,
                              AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (isInCart)
                            Container(
                              padding: EdgeInsets.all(1),
                              decoration: BoxDecoration(
                                color: AppTheme.lightTheme.colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                              child: CustomIconWidget(
                                iconName: 'check',
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                        ],
                      ),

                      SizedBox(height: 1),

                      _buildHighlightedText(
                        item['description'] as String,
                        searchQuery,
                        AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                      ),

                      SizedBox(height: 1),

                      // Dietary icons
                      Row(
                        children: [
                          if (item['isVegetarian'] == true)
                            _DietaryIcon(
                              icon: 'eco',
                              color: Colors.green,
                              tooltip: 'Vegetarian',
                            ),
                          if (item['isGlutenFree'] == true)
                            _DietaryIcon(
                              icon: 'grain',
                              color: Colors.orange,
                              tooltip: 'Gluten Free',
                            ),
                          if (item['isSpicy'] == true)
                            _DietaryIcon(
                              icon: 'local_fire_department',
                              color: Colors.red,
                              tooltip: 'Spicy',
                            ),
                          Spacer(),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomIconWidget(
                                iconName: 'star',
                                color: Colors.amber,
                                size: 14,
                              ),
                              SizedBox(width: 1),
                              Text(
                                item['rating'].toString(),
                                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      SizedBox(height: 1),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item['price'] as String,
                                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.lightTheme.colorScheme.primary,
                                ),
                              ),
                              Text(
                                item['preparationTime'] as String,
                                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),

                          // Add button
                          Container(
                            decoration: BoxDecoration(
                              color: AppTheme.lightTheme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              onPressed: onTap,
                              icon: CustomIconWidget(
                                iconName: 'add',
                                color: Colors.white,
                                size: 20,
                              ),
                              constraints: BoxConstraints(
                                minWidth: 10,
                                minHeight: 5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHighlightedText(
    String text,
    String query,
    TextStyle? style, {
    int? maxLines,
  }) {
    if (query.isEmpty) {
      return Text(
        text,
        style: style,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      );
    }

    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final spans = <TextSpan>[];

    int start = 0;
    int index = lowerText.indexOf(lowerQuery);

    while (index != -1) {
      if (index > start) {
        spans.add(TextSpan(
          text: text.substring(start, index),
          style: style,
        ));
      }

      spans.add(TextSpan(
        text: text.substring(index, index + query.length),
        style: style?.copyWith(
          backgroundColor: AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.3),
          fontWeight: FontWeight.w600,
        ),
      ));

      start = index + query.length;
      index = lowerText.indexOf(lowerQuery, start);
    }

    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: style,
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.clip,
    );
  }
}

class _DietaryIcon extends StatelessWidget {
  final String icon;
  final Color color;
  final String tooltip;

  const _DietaryIcon({
    required this.icon,
    required this.color,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Container(
        margin: EdgeInsets.only(right: 2),
        padding: EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: CustomIconWidget(
          iconName: icon,
          color: color,
          size: 14,
        ),
      ),
    );
  }
}
