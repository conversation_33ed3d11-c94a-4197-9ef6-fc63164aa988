import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class FinalOrderReviewWidget extends StatelessWidget {
  final List<Map<String, dynamic>> cartItems;
  final double subtotal;
  final double deliveryFee;
  final double serviceFee;
  final double taxes;
  final double discount;
  final double tip;
  final double total;
  final String? promoCode;
  final Map<String, dynamic> restaurantData;

  const FinalOrderReviewWidget({
    super.key,
    required this.cartItems,
    required this.subtotal,
    required this.deliveryFee,
    required this.serviceFee,
    required this.taxes,
    this.discount = 0.0,
    this.tip = 0.0,
    required this.total,
    this.promoCode,
    required this.restaurantData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppTheme.lightTheme.colorScheme.primary.withAlpha(51),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'receipt_long',
                    color: AppTheme.lightTheme.colorScheme.primary,
                    size: 20,
                  ),
                ),
                SizedBox(width: 3),
                Text(
                  'Order Review',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2),

            // Restaurant header
            Container(
              padding: EdgeInsets.all(3),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primary.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: CustomImageWidget(
                      imageUrl: restaurantData['headerImage'] as String,
                      width: 12,
                      height: 12,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 3),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          restaurantData['name'] as String,
                          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${cartItems.length} ${cartItems.length == 1 ? 'item' : 'items'}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_down',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
            ),

            SizedBox(height: 2),

            // Expandable order items
            ExpansionTile(
              title: Text(
                'View Order Details',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              children: cartItems.map((item) => _buildOrderItem(item)).toList(),
            ),

            SizedBox(height: 2),

            Divider(
              color: AppTheme.lightTheme.colorScheme.outline,
              thickness: 1,
            ),

            SizedBox(height: 2),

            // Order summary
            Text(
              'Payment Summary',
              style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1),

            _buildSummaryRow('Subtotal', subtotal),
            _buildSummaryRow('Delivery Fee', deliveryFee),
            _buildSummaryRow('Service Fee', serviceFee),
            _buildSummaryRow('Taxes & Fees', taxes),

            if (tip > 0) _buildSummaryRow('Driver Tip', tip, isHighlight: true),

            if (discount > 0)
              _buildSummaryRow(
                promoCode != null ? 'Discount ($promoCode)' : 'Discount',
                -discount,
                isDiscount: true,
              ),

            SizedBox(height: 1),
            Divider(
              color: AppTheme.lightTheme.colorScheme.outline,
              thickness: 1,
            ),
            SizedBox(height: 1),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Text(
                  '\${total.toStringAsFixed(2)}',
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> item) {
    final quantity = item['quantity'] as int;
    final price = double.parse((item['price'] as String).replaceAll('\$', ''));
    final totalPrice = price * quantity;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1, horizontal: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                quantity.toString(),
                style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: 3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name'] as String,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (item['customization'] != null) ...[
                  SizedBox(height: 0.5),
                  Text(
                    _getCustomizationText(item['customization']),
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Text(
            '{$totalPrice.toStringAsFixed(2)}',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getCustomizationText(Map<String, dynamic> customization) {
    final List<String> customizations = [];

    customization.forEach((key, value) {
      if (key != 'quantity' && value != null && value.toString().isNotEmpty) {
        switch (key) {
          case 'size':
            customizations.add('Size: $value');
            break;
          case 'spiceLevel':
            customizations.add('Spice: $value');
            break;
          case 'crust':
            customizations.add('Crust: $value');
            break;
          case 'specialInstructions':
            customizations.add('Note: $value');
            break;
          default:
            customizations.add('$key: $value');
        }
      }
    });

    return customizations.join(', ');
  }

  Widget _buildSummaryRow(String label, double amount, {bool isDiscount = false, bool isHighlight = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              color: isHighlight ? Colors.green : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              fontWeight: isHighlight ? FontWeight.w500 : FontWeight.w400,
            ),
          ),
          Text(
            '${isDiscount ? '-' : ''}\${amount.abs().toStringAsFixed(2)}',
            style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isDiscount
                  ? Colors.green
                  : isHighlight
                      ? Colors.green
                      : AppTheme.lightTheme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
