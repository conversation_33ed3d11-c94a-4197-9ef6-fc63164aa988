import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../core/app_export.dart';
import './widgets/location_permission_widget.dart';
import './widgets/registration_form_widget.dart';
import './widgets/social_registration_widget.dart';

class CustomerRegistration extends StatefulWidget {
  const CustomerRegistration({super.key});

  @override
  State<CustomerRegistration> createState() => _CustomerRegistrationState();
}

class _CustomerRegistrationState extends State<CustomerRegistration> with TickerProviderStateMixin {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ScrollController _scrollController = ScrollController();

  // Form controllers
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  // Focus nodes
  final FocusNode _fullNameFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();
  final FocusNode _confirmPasswordFocus = FocusNode();

  // Form state
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isTermsAccepted = false;
  bool _isLocationPermissionRequested = false;
  bool _isLocationPermissionGranted = false;
  bool _isLoading = false;

  // Validation states
  bool _isFullNameValid = false;
  bool _isEmailValid = false;
  bool _isPhoneValid = false;
  bool _isPasswordValid = false;
  bool _isConfirmPasswordValid = false;

  // Password strength
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.red;

  @override
  void initState() {
    super.initState();
    _setupFocusListeners();
  }

  void _setupFocusListeners() {
    _fullNameFocus.addListener(() {
      if (_fullNameFocus.hasFocus) {
        _scrollToField(0);
      }
    });

    _emailFocus.addListener(() {
      if (_emailFocus.hasFocus) {
        _scrollToField(1);
      }
    });

    _phoneFocus.addListener(() {
      if (_phoneFocus.hasFocus) {
        _scrollToField(2);
      }
    });

    _passwordFocus.addListener(() {
      if (_passwordFocus.hasFocus) {
        _scrollToField(3);
      }
    });

    _confirmPasswordFocus.addListener(() {
      if (_confirmPasswordFocus.hasFocus) {
        _scrollToField(4);
      }
    });
  }

  void _scrollToField(int fieldIndex) {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          fieldIndex * 12,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _validateFullName(String value) {
    setState(() {
      _isFullNameValid = value.trim().length >= 2 && RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim());
    });
  }

  void _validateEmail(String value) {
    setState(() {
      _isEmailValid = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);
    });
  }

  void _validatePhone(String value) {
    setState(() {
      _isPhoneValid = value.length >= 10 && RegExp(r'^[0-9]+$').hasMatch(value);
    });
  }

  void _validatePassword(String value) {
    setState(() {
      _isPasswordValid = value.length >= 8;
      _calculatePasswordStrength(value);
    });
  }

  void _validateConfirmPassword(String value) {
    setState(() {
      _isConfirmPasswordValid = value == _passwordController.text && value.isNotEmpty;
    });
  }

  void _calculatePasswordStrength(String password) {
    double strength = 0.0;
    String strengthText = '';
    Color strengthColor = Colors.red;

    if (password.isEmpty) {
      strength = 0.0;
      strengthText = '';
    } else if (password.length < 6) {
      strength = 0.2;
      strengthText = 'Weak';
      strengthColor = Colors.red;
    } else if (password.length < 8) {
      strength = 0.4;
      strengthText = 'Fair';
      strengthColor = Colors.orange;
    } else {
      strength = 0.6;
      strengthText = 'Good';
      strengthColor = Colors.blue;

      if (RegExp(r'[A-Z]').hasMatch(password)) strength += 0.1;
      if (RegExp(r'[0-9]').hasMatch(password)) strength += 0.1;
      if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) strength += 0.2;

      if (strength >= 0.8) {
        strengthText = 'Strong';
        strengthColor = Colors.green;
      }
    }

    setState(() {
      _passwordStrength = strength;
      _passwordStrengthText = strengthText;
      _passwordStrengthColor = strengthColor;
    });
  }

  bool get _isFormValid {
    return _isFullNameValid &&
        _isEmailValid &&
        _isPhoneValid &&
        _isPasswordValid &&
        _isConfirmPasswordValid &&
        _isTermsAccepted;
  }

  void _requestLocationPermission() {
    setState(() {
      _isLocationPermissionRequested = true;
    });

    // Simulate location permission request
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLocationPermissionGranted = true;
      });

      Fluttertoast.showToast(
        msg: "Location permission granted!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: AppTheme.successLight,
        textColor: Colors.white,
      );
    });
  }

  Future<void> _handleRegistration() async {
    if (!_isFormValid) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate registration API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock registration success
      HapticFeedback.lightImpact();

      Fluttertoast.showToast(
        msg: "Welcome to RestaurantHub! Account created successfully.",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: AppTheme.successLight,
        textColor: Colors.white,
      );

      // Navigate to restaurant browse
      Navigator.pushReplacementNamed(context, '/restaurant-browse');
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Registration failed. Please try again.",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: AppTheme.errorLight,
        textColor: Colors.white,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleSocialRegistration(String provider) {
    Fluttertoast.showToast(
      msg: "Registering with $provider...",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );

    // Simulate social registration
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.pushReplacementNamed(context, '/restaurant-browse');
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();

    _fullNameFocus.dispose();
    _emailFocus.dispose();
    _phoneFocus.dispose();
    _passwordFocus.dispose();
    _confirmPasswordFocus.dispose();

    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: AppTheme.lightTheme.colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.shadowLight,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: CustomIconWidget(
                        iconName: 'arrow_back',
                        color: AppTheme.textPrimaryLight,
                        size: 6,
                      ),
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Create Account',
                    style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: EdgeInsets.symmetric(horizontal: 6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 2),

                    // Welcome text
                    Text(
                      'Join RestaurantHub',
                      style: AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.primaryLight,
                      ),
                    ),
                    SizedBox(height: 1),
                    Text(
                      'Create your account to start ordering delicious food from your favorite restaurants.',
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryLight,
                      ),
                    ),

                    SizedBox(height: 4),

                    // Social registration options
                    SocialRegistrationWidget(
                      onSocialLogin: _handleSocialRegistration,
                    ),

                    SizedBox(height: 3),

                    // Divider with "or"
                    Row(
                      children: [
                        Expanded(
                          child: Divider(
                            color: AppTheme.dividerLight,
                            thickness: 1,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4),
                          child: Text(
                            'or',
                            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondaryLight,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Divider(
                            color: AppTheme.dividerLight,
                            thickness: 1,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 3),

                    // Registration form
                    Form(
                      key: _formKey,
                      child: RegistrationFormWidget(
                        fullNameController: _fullNameController,
                        emailController: _emailController,
                        phoneController: _phoneController,
                        passwordController: _passwordController,
                        confirmPasswordController: _confirmPasswordController,
                        fullNameFocus: _fullNameFocus,
                        emailFocus: _emailFocus,
                        phoneFocus: _phoneFocus,
                        passwordFocus: _passwordFocus,
                        confirmPasswordFocus: _confirmPasswordFocus,
                        isPasswordVisible: _isPasswordVisible,
                        isConfirmPasswordVisible: _isConfirmPasswordVisible,
                        isFullNameValid: _isFullNameValid,
                        isEmailValid: _isEmailValid,
                        isPhoneValid: _isPhoneValid,
                        isPasswordValid: _isPasswordValid,
                        isConfirmPasswordValid: _isConfirmPasswordValid,
                        passwordStrength: _passwordStrength,
                        passwordStrengthText: _passwordStrengthText,
                        passwordStrengthColor: _passwordStrengthColor,
                        onFullNameChanged: _validateFullName,
                        onEmailChanged: _validateEmail,
                        onPhoneChanged: _validatePhone,
                        onPasswordChanged: _validatePassword,
                        onConfirmPasswordChanged: _validateConfirmPassword,
                        onPasswordVisibilityToggle: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                        onConfirmPasswordVisibilityToggle: () {
                          setState(() {
                            _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                          });
                        },
                      ),
                    ),

                    SizedBox(height: 3),

                    // Location permission request
                    if (!_isLocationPermissionGranted)
                      LocationPermissionWidget(
                        isRequested: _isLocationPermissionRequested,
                        onRequestPermission: _requestLocationPermission,
                      ),

                    if (_isLocationPermissionGranted) SizedBox(height: 2),

                    // Terms and privacy policy
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Checkbox(
                          value: _isTermsAccepted,
                          onChanged: (value) {
                            setState(() {
                              _isTermsAccepted = value ?? false;
                            });
                          },
                          activeColor: AppTheme.accentLight,
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _isTermsAccepted = !_isTermsAccepted;
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.only(top: 3),
                              child: RichText(
                                text: TextSpan(
                                  style: AppTheme.lightTheme.textTheme.bodySmall,
                                  children: [
                                    const TextSpan(
                                      text: 'I agree to the ',
                                    ),
                                    TextSpan(
                                      text: 'Terms of Service',
                                      style: TextStyle(
                                        color: AppTheme.primaryLight,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                    const TextSpan(
                                      text: ' and ',
                                    ),
                                    TextSpan(
                                      text: 'Privacy Policy',
                                      style: TextStyle(
                                        color: AppTheme.primaryLight,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 4),

                    // Create account button
                    SizedBox(
                      width: double.infinity,
                      height: 7,
                      child: ElevatedButton(
                        onPressed: _isFormValid && !_isLoading ? _handleRegistration : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isFormValid ? AppTheme.primaryLight : AppTheme.textDisabledLight,
                          foregroundColor: Colors.white,
                          elevation: _isFormValid ? 2 : 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: 6,
                                height: 6,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : Text(
                                'Create Account',
                                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),

                    SizedBox(height: 3),

                    // Login link
                    Center(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pushReplacementNamed(context, '/customer-login');
                        },
                        child: RichText(
                          text: TextSpan(
                            style: AppTheme.lightTheme.textTheme.bodyMedium,
                            children: [
                              const TextSpan(
                                text: 'Already have an account? ',
                              ),
                              TextSpan(
                                text: 'Sign In',
                                style: TextStyle(
                                  color: AppTheme.primaryLight,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 4),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
