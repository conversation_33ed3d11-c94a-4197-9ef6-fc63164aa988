import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class OrderSummaryWidget extends StatefulWidget {
  final List<Map<String, dynamic>> orderItems;
  final Map<String, dynamic> orderData;
  final VoidCallback? onContactSupport;

  const OrderSummaryWidget({
    super.key,
    required this.orderItems,
    required this.orderData,
    this.onContactSupport,
  });

  @override
  State<OrderSummaryWidget> createState() => _OrderSummaryWidgetState();
}

class _OrderSummaryWidgetState extends State<OrderSummaryWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with expand/collapse
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order Summary',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Row(
                children: [
                  Text(
                    '${widget.orderItems.length} items',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(width: 2),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    child: AnimatedRotation(
                      turns: _isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: CustomIconWidget(
                        iconName: 'expand_more',
                        size: 20,
                        color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 2),

          // Order items list
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isExpanded ? null : 0,
            child: _isExpanded
                ? Column(
                    children: [
                      ...widget.orderItems.map((item) => Container(
                            margin: EdgeInsets.only(bottom: 2),
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: CustomImageWidget(
                                    imageUrl: item['image'] as String,
                                    width: 12,
                                    height: 12,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                SizedBox(width: 3),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item['name'] as String,
                                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      if (item['customization'] != null && (item['customization'] as Map).isNotEmpty)
                                        Text(
                                          _getCustomizationText(item['customization'] as Map),
                                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      'x${item['quantity']}',
                                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      item['price'] as String,
                                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: AppTheme.lightTheme.colorScheme.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          )),
                      Divider(
                        color: AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                      ),
                      SizedBox(height: 2),
                    ],
                  )
                : const SizedBox.shrink(),
          ),

          // Order total and details
          Column(
            children: [
              _buildSummaryRow('Subtotal', widget.orderData['subtotal'] as String),
              _buildSummaryRow('Delivery Fee', widget.orderData['deliveryFee'] as String),
              _buildSummaryRow('Service Fee', widget.orderData['serviceFee'] as String),
              _buildSummaryRow('Tax', widget.orderData['tax'] as String),
              if (widget.orderData['discount'] != null)
                _buildSummaryRow('Discount', '-${widget.orderData['discount']}',
                    textColor: AppTheme.lightTheme.colorScheme.tertiary),
              SizedBox(height: 1),
              Divider(
                color: AppTheme.lightTheme.colorScheme.outline.withAlpha(51),
                thickness: 1,
              ),
              SizedBox(height: 1),
              _buildSummaryRow(
                'Total',
                widget.orderData['total'] as String,
                isTotal: true,
              ),
            ],
          ),

          SizedBox(height: 3),

          // Payment method and delivery info
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(3),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'payment',
                      size: 20,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                    SizedBox(width: 2),
                    Text(
                      'Payment: ${widget.orderData['paymentMethod']}',
                      style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 1),
                Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'location_on',
                      size: 20,
                      color: AppTheme.lightTheme.colorScheme.primary,
                    ),
                    SizedBox(width: 2),
                    Expanded(
                      child: Text(
                        'Deliver to: ${widget.orderData['deliveryAddress']}',
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 3),

          // Contact support button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: widget.onContactSupport,
              icon: CustomIconWidget(
                iconName: 'support_agent',
                size: 20,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
              label: Text('Contact Support'),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false, Color? textColor}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: (isTotal ? AppTheme.lightTheme.textTheme.titleMedium : AppTheme.lightTheme.textTheme.bodyMedium)
                ?.copyWith(
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: textColor,
            ),
          ),
          Text(
            value,
            style: (isTotal ? AppTheme.lightTheme.textTheme.titleMedium : AppTheme.lightTheme.textTheme.bodyMedium)
                ?.copyWith(
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
              color: textColor ?? (isTotal ? AppTheme.lightTheme.colorScheme.primary : null),
            ),
          ),
        ],
      ),
    );
  }

  String _getCustomizationText(Map customization) {
    List<String> customizations = [];

    if (customization['size'] != null) {
      customizations.add('Size: ${customization['size']}');
    }
    if (customization['spiceLevel'] != null) {
      customizations.add('Spice: ${customization['spiceLevel']}');
    }
    if (customization['extras'] != null && (customization['extras'] as List).isNotEmpty) {
      customizations.add('Extras: ${(customization['extras'] as List).join(', ')}');
    }
    if (customization['notes'] != null && (customization['notes'] as String).isNotEmpty) {
      customizations.add('Notes: ${customization['notes']}');
    }

    return customizations.join(' • ');
  }
}
