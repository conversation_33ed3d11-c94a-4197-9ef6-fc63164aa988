import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class DriverInfoWidget extends StatelessWidget {
  final Map<String, dynamic> driverData;
  final VoidCallback? onCallDriver;
  final VoidCallback? onMessageDriver;

  const DriverInfoWidget({
    super.key,
    required this.driverData,
    this.onCallDriver,
    this.onMessageDriver,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.lightTheme.colorScheme.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Delivery Driver',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2),
          Row(
            children: [
              // Driver photo with online indicator
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CustomImageWidget(
                      imageUrl: driverData['photo'] as String,
                      width: 16,
                      height: 16,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(width: 3),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      driverData['name'] as String,
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.5),
                    Row(
                      children: [
                        Row(
                          children: List.generate(5, (index) {
                            return CustomIconWidget(
                              iconName: 'star',
                              size: 14,
                              color: index < (driverData['rating'] as double).floor()
                                  ? Colors.amber
                                  : Colors.grey.shade300,
                            );
                          }),
                        ),
                        SizedBox(width: 2),
                        Text(
                          '${driverData['rating']} (${driverData['totalDeliveries']} deliveries)',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.5),
                    Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'directions_car',
                          size: 16,
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(width: 1),
                        Text(
                          '${driverData['vehicle']['make']} ${driverData['vehicle']['model']} • ${driverData['vehicle']['licensePlate']}',
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 3),

          // Communication buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: onCallDriver,
                  icon: CustomIconWidget(
                    iconName: 'phone',
                    size: 20,
                    color: Colors.white,
                  ),
                  label: Text('Call'),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    backgroundColor: AppTheme.lightTheme.colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              SizedBox(width: 3),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onMessageDriver,
                  icon: CustomIconWidget(
                    iconName: 'chat',
                    size: 20,
                    color: AppTheme.lightTheme.colorScheme.primary,
                  ),
                  label: Text('Message'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 2),
                  ),
                ),
              ),
            ],
          ),

          // Driver location update
          SizedBox(height: 2),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(3),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.tertiary.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'location_on',
                  size: 20,
                  color: AppTheme.lightTheme.colorScheme.tertiary,
                ),
                SizedBox(width: 2),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driverData['currentLocation'] as String,
                        style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Last updated: ${driverData['lastUpdate']}',
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
